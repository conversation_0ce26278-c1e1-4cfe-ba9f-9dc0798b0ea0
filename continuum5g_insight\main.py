# Continuum5G Insight - Main Application
# This script will be used to run the Continuum5G Insight system or a demo.

import os
import sys
import logging

# Adjust sys.path to include the 'continuum5g_insight_project' directory if main.py is one level inside it
# This allows `from agents...` to work when main.py is in continuum5g_insight_project/continuum5g_insight/
current_script_path = os.path.abspath(__file__)
project_root_from_main = os.path.abspath(os.path.join(os.path.dirname(current_script_path), '..'))

if project_root_from_main not in sys.path:
    sys.path.insert(0, project_root_from_main)

# Now that sys.path is adjusted, we can import from the continuum5g_insight package
from continuum5g_insight.agents.orchestration_agent import OrchestrationAgent
# MODEL_PATH_BINARY, MODEL_PATH_MULTICLASS etc. are defined within their respective agents
# and OrchestrationAgent imports them from there.

# Define the root directory of the project to help resolve paths
# This assumes main.py is in continuum5g_insight_project/continuum5g_insight/
PROJECT_ROOT = project_root_from_main # Use the calculated project root

# Define base paths for data, models, and KBs relative to PROJECT_ROOT
# These will be used by the Orchestrator if it needs to construct full paths, 
# or for logging/display purposes here.
DATA_BASE_PATH_ABS = os.path.join(PROJECT_ROOT, "data", "sample_inputs")
MODELS_BASE_PATH_ABS = os.path.join(PROJECT_ROOT, "models", "rf_models") # Base for RF models
KB_BASE_PATH_ABS = os.path.join(PROJECT_ROOT, "continuum5g_insight", "knowledge_bases") # Base for KBs

DATASET_FILENAME = "5g_nidd_dataset.csv.placeholder" # Filename for Orchestrator

# For displaying paths to user
# These are constructed for display and verification here.
DISPLAY_DATASET_FILE_PATH = os.path.join(DATA_BASE_PATH_ABS, DATASET_FILENAME)
# Model and KB paths are now more abstractly handled by agents but we can show the base dirs.
DISPLAY_MODEL_DIR_PATH = MODELS_BASE_PATH_ABS 
DISPLAY_KB_DIR_PATH = KB_BASE_PATH_ABS


if __name__ == '__main__':
    # Setup basic logging to console for the main application
    # Agents will have their own loggers, but this catches Orchestrator logs if not configured elsewhere.
    logging.basicConfig(level=logging.INFO, 
                        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                        stream=sys.stdout) # Ensure sys is imported
    
    main_logger = logging.getLogger("Continuum5GInsightMain")
    main_logger.info("--- Continuum5G Insight System Orchestration --- Awakening --- ")
    main_logger.info(f"Project Root (calculated by main.py): {PROJECT_ROOT}")
    main_logger.info(f"IMPORTANT: This system uses placeholder paths for models and datasets.")
    main_logger.info(f"           Ensure actual files are in place and feature engineering in anomaly_detection_agent.py is customized.")
    main_logger.info(f"Orchestrator will attempt to load dataset: '{DATASET_FILENAME}' from base: '{DATA_BASE_PATH_ABS}' (Resolved: {DISPLAY_DATASET_FILE_PATH} - Exists: {os.path.exists(DISPLAY_DATASET_FILE_PATH)})")
    main_logger.info(f"AnomalyDetectionAgent expects models relative to its own path, using base dir: '{DISPLAY_MODEL_DIR_PATH}' (Exists: {os.path.exists(DISPLAY_MODEL_DIR_PATH)})")
    main_logger.info(f"RAG Agents expect KBs relative to their own paths, using base dir: '{DISPLAY_KB_DIR_PATH}' (Exists: {os.path.exists(DISPLAY_KB_DIR_PATH)})")
    main_logger.info("------------------------------------------------------------------------------------")

    # Initialize and run the orchestrator
    # Orchestrator's constructor takes base paths that it will resolve relative to its own file location.
    # So, we provide paths relative to where orchestration_agent.py is.
    # Assuming main.py is in continuum5g_insight/ and agents are in continuum5g_insight/agents/
    # Path from main.py to agents/ is 'agents/'
    # Path from agents/ back to data/ is '../../data/sample_inputs'
    # Path from agents/ back to models/ is '../../models/rf_models'
    # Path from agents/ to its own knowledge_bases/ is '../knowledge_bases' (this is handled by agents themselves)
    
    orchestrator = OrchestrationAgent(
        dataset_base_path="../../data/sample_inputs", # Relative to orchestration_agent.py
        models_base_path="../../models/rf_models",   # Relative to orchestration_agent.py (though models are loaded by AnomalyDetectionAgent relative to itself)
        kb_base_path="../knowledge_bases" # Relative to orchestration_agent.py (though KBs are loaded by RAG agents relative to themselves)
    )
    
    main_logger.info("OrchestrationAgent initialized by main.py.")

    # Run the processing stream
    # max_records can be increased for more extensive testing.
    # stream_frequency_hz can be adjusted to control speed for demos.
    try:
        orchestrator.process_data_stream(
            dataset_filename=DATASET_FILENAME, 
            max_records_to_process=5, # Process 5 records for a quick demo run
            stream_frequency_hz=2 # Process 2 records per second approx.
        )
    except Exception as e:
        main_logger.error(f"An error occurred during orchestration: {e}", exc_info=True)
    finally:
        main_logger.info("--- Continuum5G Insight System Orchestration --- Execution Attempt Complete --- ")
