"""
Streamlit Dashboard for 5G Security Analytics - Real-time Monitoring
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import time
import logging
from typing import Dict, Any, List, Optional, Tuple
import json
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.data_loader import DataLoader
from src.core.batch_processor import BatchProcessor
from src.models.anomaly_detector import AnomalyDetector

logger = logging.getLogger(__name__)


# Shared state file for communication between Gradio and Streamlit
STATE_FILE = "processing_state.json"

def load_state():
    """Load processing state from file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except:
            pass
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

def save_state(state):
    """Save processing state to file"""
    try:
        with open(STATE_FILE, 'w') as f:
            json.dump(state, f)
    except Exception as e:
        logger.error(f"Error saving state: {e}")

class StreamlitDashboard:
    """
    Streamlit dashboard for real-time monitoring
    """

    def __init__(self):
        """Initialize the dashboard"""
        self.state = load_state()

        # Initialize components if needed
        self.data_loader = None
        self.detector = None
        self.batch_processor = None

        if os.path.exists("data/sample_inputs/5g_nidd_dataset.csv"):
            self.data_loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")

        if os.path.exists("models/rf_models"):
            self.detector = AnomalyDetector("models/rf_models")
            if self.detector.binary_model is not None:
                self.batch_processor = BatchProcessor(self.detector)

    def get_system_status(self):
        """Get current system status"""
        # Dataset status
        if self.data_loader and self.data_loader.df is not None:
            dataset_info = self.data_loader.get_dataset_info()
            dataset_status = f"✅ Dataset: {dataset_info['total_records']:,} records"
        else:
            dataset_status = "❌ Dataset not loaded"

        # Model status
        if self.detector and self.detector.binary_model is not None:
            model_status = "✅ Models loaded"
        else:
            model_status = "❌ Models not loaded"

        # Processing status
        if self.state["is_processing"]:
            processing_status = f"🔄 Processing batch {self.state['processing_stats']['current_batch']}"
        else:
            processing_status = "⏸️ Ready"

        return dataset_status, model_status, processing_status

    def create_anomaly_timeline_chart(self):
        """Create anomaly detection timeline chart"""
        results = self.state.get("current_results", [])

        if not results:
            fig = go.Figure()
            fig.add_annotation(text="No data to display",
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            fig.update_layout(title="Anomaly Detection Timeline", height=400)
            return fig

        # Prepare data
        timestamps = list(range(len(results)))
        anomaly_scores = [r.get("binary_confidence", 0) for r in results]
        is_anomaly = [r.get("is_anomaly", False) for r in results]

        fig = go.Figure()

        # Confidence score line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=anomaly_scores,
            mode='lines',
            name='Anomaly Confidence',
            line=dict(color='blue', width=2)
        ))

        # Anomaly markers
        anomaly_x = [i for i, is_anom in enumerate(is_anomaly) if is_anom]
        anomaly_y = [anomaly_scores[i] for i in anomaly_x]

        fig.add_trace(go.Scatter(
            x=anomaly_x,
            y=anomaly_y,
            mode='markers',
            name='Detected Anomalies',
            marker=dict(color='red', size=10, symbol='triangle-up')
        ))

        # Threshold line
        fig.add_hline(y=0.3, line_dash="dash", line_color="orange",
                     annotation_text="Detection Threshold (0.3)")

        fig.update_layout(
            title="Anomaly Detection Timeline",
            xaxis_title="Record Index",
            yaxis_title="Anomaly Confidence",
            height=400,
            showlegend=True
        )

        return fig

    def create_attack_types_pie_chart(self):
        """Create attack types distribution pie chart"""
        results = self.state.get("current_results", [])

        # Count attack types
        attack_types = {}
        normal_count = 0

        for result in results:
            if result.get("is_anomaly", False):
                attack_type = result.get("attack_type", "Unknown")
                attack_types[attack_type] = attack_types.get(attack_type, 0) + 1
            else:
                normal_count += 1

        # Add normal traffic to the pie chart
        if normal_count > 0:
            attack_types["Normal"] = normal_count

        if not attack_types:
            fig = go.Figure()
            fig.add_annotation(text="No data to display",
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            fig.update_layout(title="Traffic Distribution", height=400)
            return fig

        # Create pie chart
        labels = list(attack_types.keys())
        values = list(attack_types.values())

        # Color mapping
        colors = []
        for label in labels:
            if label == "Normal":
                colors.append("#2E8B57")  # Green for normal
            elif "Flood" in label:
                colors.append("#DC143C")  # Red for flood attacks
            elif "Scan" in label:
                colors.append("#FF8C00")  # Orange for scan attacks
            else:
                colors.append("#8B0000")  # Dark red for other attacks

        fig = go.Figure(data=[
            go.Pie(
                labels=labels,
                values=values,
                hole=0.4,  # Donut chart
                marker=dict(colors=colors, line=dict(color='#FFFFFF', width=2)),
                textinfo='label+percent',
                textposition='outside'
            )
        ])

        fig.update_layout(
            title="Traffic Distribution",
            height=400,
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="middle",
                y=0.5,
                xanchor="left",
                x=1.05
            )
        )

        return fig

    def create_requests_over_time_chart(self):
        """Create requests over time chart (like in original project)"""
        results = self.state.get("current_results", [])

        if not results:
            fig = go.Figure()
            fig.add_annotation(text="No data to display",
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            fig.update_layout(title="Total Requests Over Time", height=400)
            return fig

        # Prepare time series data
        timestamps = list(range(len(results)))
        cumulative_count = list(range(1, len(results) + 1))

        # Calculate blocked and allowed counts over time
        blocked_count = []
        allowed_count = []

        blocked_total = 0
        allowed_total = 0

        for result in results:
            if result.get("is_anomaly", False):
                blocked_total += 1
            else:
                allowed_total += 1

            blocked_count.append(blocked_total)
            allowed_count.append(allowed_total)

        fig = go.Figure()

        # Total requests line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=cumulative_count,
            mode='lines',
            name='Total Requests',
            line=dict(color='blue', width=2),
            showlegend=True
        ))

        # Allowed requests line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=allowed_count,
            mode='lines',
            name='Allowed Requests',
            line=dict(color='green', width=1),
            showlegend=True
        ))

        # Blocked requests line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=blocked_count,
            mode='lines',
            name='Blocked Requests',
            line=dict(color='red', width=1),
            showlegend=True
        ))

        fig.update_layout(
            title="Total Requests Over Time",
            xaxis_title="Record Index",
            yaxis_title="Request Count",
            height=400,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig

    def create_throughput_chart(self):
        """Create real-time throughput chart"""
        # This would show processing rate over time
        # For now, show current stats
        stats = self.state.get("processing_stats", {})

        fig = go.Figure()
        fig.add_trace(go.Indicator(
            mode = "gauge+number",
            value = stats.get("processing_rate", 0),
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Processing Rate (records/sec)"},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 25], 'color': "lightgray"},
                    {'range': [25, 50], 'color': "gray"},
                    {'range': [50, 100], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))

        fig.update_layout(height=300)
        return fig

    def get_recent_detections(self, limit=10):
        """Get recent anomaly detections"""
        results = self.state.get("current_results", [])
        recent_anomalies = [r for r in results[-50:] if r.get("is_anomaly", False)][-limit:]

        if not recent_anomalies:
            return "No recent anomalies detected"

        detection_text = "Recent Anomaly Detections:\n\n"
        for i, detection in enumerate(recent_anomalies, 1):
            detection_text += f"{i}. Attack Type: {detection.get('attack_type', 'Unknown')}\n"
            detection_text += f"   Confidence: {detection.get('binary_confidence', 0):.3f}\n"
            detection_text += f"   Time: {detection.get('total_processing_time_ms', 0):.1f}ms\n\n"

        return detection_text

    def get_logs(self):
        """Get recent log messages"""
        logs = self.state.get("log_messages", [])
        return "\n".join(logs[-20:]) if logs else "No logs available"


def create_streamlit_dashboard():
    """Create the Streamlit dashboard"""

    # Page config
    st.set_page_config(
        page_title="5G Security Analytics - Real-time Monitor",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize dashboard
    dashboard = StreamlitDashboard()

    # Initialize session state for auto-refresh
    if 'last_refresh' not in st.session_state:
        st.session_state.last_refresh = time.time()
    if 'auto_refresh_enabled' not in st.session_state:
        st.session_state.auto_refresh_enabled = True

    # Header
    st.title("🛡️ 5G Security Analytics - Real-time Monitor")
    st.markdown("Live monitoring dashboard for 5G network security analysis")

    # Auto-refresh control
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        auto_refresh = st.checkbox("Auto-refresh", value=st.session_state.auto_refresh_enabled)
        st.session_state.auto_refresh_enabled = auto_refresh
    with col2:
        refresh_interval = st.selectbox("Interval", [1, 2, 3, 5], index=1, format_func=lambda x: f"{x}s")
    with col3:
        if st.button("🔄 Manual Refresh"):
            st.rerun()

    # Refresh state
    dashboard.state = load_state()

    # Status section
    st.markdown("---")
    col1, col2, col3 = st.columns(3)

    dataset_status, model_status, processing_status = dashboard.get_system_status()

    with col1:
        st.metric("Dataset Status", dataset_status)
    with col2:
        st.metric("Model Status", model_status)
    with col3:
        st.metric("Processing Status", processing_status)

    # Main metrics
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)

    stats = dashboard.state.get("processing_stats", {})

    with col1:
        st.metric(
            "Total Processed",
            f"{stats.get('total_processed', 0):,}",
            delta=None
        )

    with col2:
        st.metric(
            "Total Anomalies",
            f"{stats.get('total_anomalies', 0):,}",
            delta=None
        )

    with col3:
        anomaly_rate = 0
        if stats.get('total_processed', 0) > 0:
            anomaly_rate = (stats.get('total_anomalies', 0) / stats.get('total_processed', 0)) * 100
        st.metric(
            "Anomaly Rate",
            f"{anomaly_rate:.1f}%",
            delta=None
        )

    with col4:
        st.metric(
            "Processing Rate",
            f"{stats.get('processing_rate', 0):.1f} rec/sec",
            delta=None
        )

    # Charts section
    st.markdown("---")

    # Row 1: Timeline (larger) and Traffic Distribution (smaller)
    col1, col2 = st.columns([3, 2])  # 3:2 ratio - left side bigger

    with col1:
        st.subheader("Anomaly Detection Timeline")
        timeline_chart = dashboard.create_anomaly_timeline_chart()
        st.plotly_chart(timeline_chart, use_container_width=True)

    with col2:
        st.subheader("Traffic Distribution")
        pie_chart = dashboard.create_attack_types_pie_chart()
        st.plotly_chart(pie_chart, use_container_width=True)

    # Row 2: Requests Over Time and Processing Rate
    col1, col2 = st.columns([3, 2])  # Same ratio as above

    with col1:
        st.subheader("Total Requests Over Time")
        requests_chart = dashboard.create_requests_over_time_chart()
        st.plotly_chart(requests_chart, use_container_width=True)

    with col2:
        st.subheader("Real-time Processing Rate")
        throughput_chart = dashboard.create_throughput_chart()
        st.plotly_chart(throughput_chart, use_container_width=True)

    # Details section
    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Recent Detections")
        recent_detections = dashboard.get_recent_detections()
        st.text_area("Latest Anomalies", recent_detections, height=300)

    with col2:
        st.subheader("System Logs")
        logs = dashboard.get_logs()
        st.text_area("Processing Logs", logs, height=300)

    # Sidebar with additional info
    st.sidebar.header("Dashboard Info")
    st.sidebar.info("This dashboard shows real-time monitoring of the 5G security analytics system.")
    st.sidebar.info("Use the Gradio control panel to start/stop processing and configure settings.")

    # Current settings display
    st.sidebar.markdown("### Current Settings")
    st.sidebar.text(f"Batch Size: {dashboard.state.get('batch_size', 100)}")
    st.sidebar.text(f"Max Batches: {dashboard.state.get('max_batches', 10)}")
    st.sidebar.text(f"RPS: {dashboard.state.get('requests_per_second', 10.0)}")
    st.sidebar.text(f"Mode: {dashboard.state.get('processing_mode', 'Manual')}")

    # Auto-refresh logic with adaptive timing
    current_time = time.time()
    is_processing = dashboard.state.get("is_processing", False)

    # Processing status indicator
    if is_processing:
        st.sidebar.success("🔄 Processing Active")
        # More frequent refresh when processing (every 1 second)
        if st.session_state.auto_refresh_enabled and current_time - st.session_state.last_refresh > 1:
            st.session_state.last_refresh = current_time
            st.rerun()
    else:
        st.sidebar.info("⏸️ Ready")
        # Normal refresh interval when not processing
        if st.session_state.auto_refresh_enabled and current_time - st.session_state.last_refresh > refresh_interval:
            st.session_state.last_refresh = current_time
            st.rerun()

    # Manual refresh in sidebar
    if st.sidebar.button("🔄 Refresh Now"):
        st.session_state.last_refresh = time.time()
        st.rerun()

    # Show last update time
    st.sidebar.caption(f"Last updated: {time.strftime('%H:%M:%S', time.localtime(st.session_state.last_refresh))}")


if __name__ == "__main__":
    create_streamlit_dashboard()
        
    def add_log(self, message: str, level: str = "INFO"):
        """Add a log message"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_messages.append(f"[{timestamp}] {level}: {message}")
        if len(self.log_messages) > 100:  # Keep only last 100 messages
            self.log_messages.pop(0)
        logger.info(message)
    
    def get_system_status(self) -> Tuple[str, str, str, str]:
        """
        Get current system status
        
        Returns:
            Tuple of (dataset_status, model_status, processing_status, performance_info)
        """
        # Dataset status
        if self.data_loader.df is not None:
            dataset_info = self.data_loader.get_dataset_info()
            dataset_status = f"✅ Dataset loaded: {dataset_info['total_records']:,} records"
        else:
            dataset_status = "❌ Dataset not loaded"
        
        # Model status
        if (self.detector.binary_model is not None and 
            self.detector.multiclass_model is not None):
            model_status = "✅ Models loaded successfully"
        else:
            model_status = "❌ Models not loaded"
        
        # Processing status
        if self.is_processing:
            processing_status = f"🔄 Processing... Batch {self.processing_stats['current_batch']}"
        else:
            processing_status = "⏸️ Ready to process"
        
        # Performance info
        stats = self.batch_processor.get_stats()
        if stats["total_records"] > 0:
            performance_info = (
                f"📊 Processed: {stats['total_records']:,} records | "
                f"Anomalies: {stats['total_anomalies']:,} | "
                f"Rate: {stats['throughput_records_per_sec']:.1f} rec/sec"
            )
        else:
            performance_info = "📊 No processing statistics yet"
        
        return dataset_status, model_status, processing_status, performance_info
    
    def process_batch_sync(self, batch_size: int, max_batches: int, 
                          progress_callback=None) -> List[Dict[str, Any]]:
        """
        Process data in batches synchronously
        
        Args:
            batch_size: Size of each batch
            max_batches: Maximum number of batches to process
            progress_callback: Optional progress callback
            
        Returns:
            List of processing results
        """
        if self.data_loader.df is None:
            self.add_log("No dataset loaded", "ERROR")
            return []
        
        self.is_processing = True
        self.current_results = []
        self.processing_stats["current_batch"] = 0
        
        try:
            self.add_log(f"Starting batch processing: {batch_size} records/batch, max {max_batches} batches")
            
            for batch_num in range(max_batches):
                if not self.is_processing:  # Check for stop signal
                    break
                
                # Get batch data
                batch_df = self.data_loader.get_batch(batch_size)
                if batch_df is None or batch_df.empty:
                    self.add_log("No more data to process")
                    break
                
                self.processing_stats["current_batch"] = batch_num + 1
                
                # Process batch
                batch_data = batch_df.to_dict('records')
                batch_results = self.batch_processor.process_batch_sequential(batch_data)
                
                # Update results and stats
                self.current_results.extend(batch_results)
                anomaly_count = sum(1 for r in batch_results if r.get("is_anomaly", False))
                
                self.processing_stats["total_processed"] += len(batch_results)
                self.processing_stats["total_anomalies"] += anomaly_count
                
                # Update throughput
                for _ in batch_results:
                    self.throughput_calc.add_event()
                
                self.processing_stats["processing_rate"] = self.throughput_calc.get_throughput()
                
                self.add_log(f"Batch {batch_num + 1} completed: {len(batch_results)} records, {anomaly_count} anomalies")
                
                if progress_callback:
                    progress_callback(batch_num + 1, max_batches)
                
                # Small delay to prevent UI freezing
                time.sleep(0.1)
            
            self.add_log(f"Processing completed: {self.processing_stats['total_processed']} total records")
            
        except Exception as e:
            self.add_log(f"Error during processing: {e}", "ERROR")
        finally:
            self.is_processing = False
        
        return self.current_results
    
    def stop_processing(self):
        """Stop current processing"""
        self.is_processing = False
        self.add_log("Processing stopped by user")
    
    def get_results_summary(self) -> Dict[str, Any]:
        """Get summary of current results"""
        if not self.current_results:
            return {"message": "No results available"}
        
        total_records = len(self.current_results)
        anomalies = sum(1 for r in self.current_results if r.get("is_anomaly", False))
        
        # Count attack types
        attack_types = {}
        for result in self.current_results:
            if result.get("is_anomaly", False):
                attack_type = result.get("attack_type", "Unknown")
                attack_types[attack_type] = attack_types.get(attack_type, 0) + 1
        
        return {
            "total_records": total_records,
            "total_anomalies": anomalies,
            "anomaly_rate": (anomalies / total_records * 100) if total_records > 0 else 0,
            "attack_types": attack_types,
            "processing_stats": self.batch_processor.get_stats()
        }
    
    def create_anomaly_chart(self) -> go.Figure:
        """Create anomaly detection chart"""
        if not self.current_results:
            fig = go.Figure()
            fig.add_annotation(text="No data to display", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            return fig
        
        # Prepare data for plotting
        timestamps = list(range(len(self.current_results)))
        anomaly_scores = [r.get("binary_confidence", 0) for r in self.current_results]
        is_anomaly = [r.get("is_anomaly", False) for r in self.current_results]
        
        fig = go.Figure()
        
        # Add confidence score line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=anomaly_scores,
            mode='lines',
            name='Anomaly Confidence',
            line=dict(color='blue', width=1)
        ))
        
        # Add anomaly markers
        anomaly_x = [i for i, is_anom in enumerate(is_anomaly) if is_anom]
        anomaly_y = [anomaly_scores[i] for i in anomaly_x]
        
        fig.add_trace(go.Scatter(
            x=anomaly_x,
            y=anomaly_y,
            mode='markers',
            name='Detected Anomalies',
            marker=dict(color='red', size=8, symbol='triangle-up')
        ))
        
        # Add threshold line
        fig.add_hline(y=0.3, line_dash="dash", line_color="orange", 
                     annotation_text="Detection Threshold")
        
        fig.update_layout(
            title="Anomaly Detection Results",
            xaxis_title="Record Index",
            yaxis_title="Anomaly Confidence",
            height=400
        )
        
        return fig
    
    def create_attack_types_chart(self) -> go.Figure:
        """Create attack types distribution chart"""
        summary = self.get_results_summary()
        attack_types = summary.get("attack_types", {})
        
        if not attack_types:
            fig = go.Figure()
            fig.add_annotation(text="No anomalies detected", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            return fig
        
        fig = go.Figure(data=[
            go.Bar(x=list(attack_types.keys()), 
                   y=list(attack_types.values()),
                   marker_color='red')
        ])
        
        fig.update_layout(
            title="Detected Attack Types",
            xaxis_title="Attack Type",
            yaxis_title="Count",
            height=400
        )
        
        return fig
    
    def get_recent_detections(self, limit: int = 10) -> str:
        """Get recent anomaly detections as formatted text"""
        if not self.current_results:
            return "No detections yet"
        
        recent_anomalies = [
            r for r in self.current_results[-50:] 
            if r.get("is_anomaly", False)
        ][-limit:]
        
        if not recent_anomalies:
            return "No recent anomalies detected"
        
        output = "Recent Anomaly Detections:\n\n"
        for i, detection in enumerate(recent_anomalies, 1):
            output += f"{i}. Attack Type: {detection.get('attack_type', 'Unknown')}\n"
            output += f"   Confidence: {detection.get('binary_confidence', 0):.3f}\n"
            output += f"   Processing Time: {detection.get('total_processing_time_ms', 0):.1f}ms\n\n"
        
        return output
    
    def get_logs(self) -> str:
        """Get recent log messages"""
        return "\n".join(self.log_messages[-20:])  # Last 20 messages


# This file now only contains Streamlit dashboard functionality
# Gradio control panel is in control_panel.py
