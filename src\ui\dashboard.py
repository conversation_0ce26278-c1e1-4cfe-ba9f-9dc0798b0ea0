import streamlit as st
import plotly.graph_objects as go
import time
import json
import os
import uuid
from typing import Dict, List, Any

# Shared state file for communication between Gradio and Streamlit
STATE_FILE = "processing_state.json"

def load_state():
    """Load processing state from file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            st.error(f"Error loading state: {e}")
    
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

class StreamlitDashboard:
    """Simple Streamlit Dashboard with efficient updates"""
    
    def __init__(self):
        self.state = load_state()
        # Generate unique session ID for this dashboard instance
        if 'dashboard_session_id' not in st.session_state:
            st.session_state.dashboard_session_id = str(uuid.uuid4())[:8]
        self.session_id = st.session_state.dashboard_session_id
    
    def get_unique_key(self, base_key: str) -> str:
        """Generate unique key for Streamlit components"""
        # Use current time in milliseconds + session ID for uniqueness
        timestamp = int(time.time() * 1000)
        return f"{base_key}_{self.session_id}_{timestamp}"
    
    def create_anomaly_timeline_chart(self):
        """Create anomaly detection timeline chart"""
        results = self.state.get("current_results", [])
        
        if not results:
            fig = go.Figure()
            fig.add_annotation(text="No data to display", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            fig.update_layout(title="Anomaly Detection Timeline", height=400)
            return fig
        
        # Prepare data
        timestamps = list(range(len(results)))
        anomaly_scores = [r.get("binary_confidence", 0) for r in results]
        is_anomaly = [r.get("is_anomaly", False) for r in results]
        
        fig = go.Figure()
        
        # Confidence score line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=anomaly_scores,
            mode='lines',
            name='Anomaly Confidence',
            line=dict(color='blue', width=2)
        ))
        
        # Anomaly markers
        anomaly_x = [i for i, is_anom in enumerate(is_anomaly) if is_anom]
        anomaly_y = [anomaly_scores[i] for i in anomaly_x]
        
        if anomaly_x:
            fig.add_trace(go.Scatter(
                x=anomaly_x,
                y=anomaly_y,
                mode='markers',
                name='Detected Anomalies',
                marker=dict(color='red', size=8, symbol='triangle-up')
            ))
        
        # Threshold line
        fig.add_hline(y=0.5, line_dash="dash", line_color="orange", 
                     annotation_text="Detection Threshold")
        
        fig.update_layout(
            title="Anomaly Detection Timeline",
            xaxis_title="Record Index",
            yaxis_title="Anomaly Confidence",
            height=400,
            showlegend=True
        )
        
        return fig
    
    def create_attack_types_pie_chart(self):
        """Create attack types distribution pie chart"""
        results = self.state.get("current_results", [])
        
        # Count attack types
        attack_types = {}
        normal_count = 0
        
        for result in results:
            if result.get("is_anomaly", False):
                attack_type = result.get("attack_type", "Unknown")
                attack_types[attack_type] = attack_types.get(attack_type, 0) + 1
            else:
                normal_count += 1
        
        # Add normal traffic to the pie chart
        if normal_count > 0:
            attack_types["Normal"] = normal_count
        
        if not attack_types:
            fig = go.Figure()
            fig.add_annotation(text="No data to display", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            fig.update_layout(title="Traffic Distribution", height=400)
            return fig
        
        # Create pie chart
        labels = list(attack_types.keys())
        values = list(attack_types.values())
        
        # Color mapping
        colors = []
        for label in labels:
            if label == "Normal":
                colors.append("#2E8B57")  # Green for normal
            elif "Flood" in label:
                colors.append("#DC143C")  # Red for flood attacks
            elif "Scan" in label:
                colors.append("#FF8C00")  # Orange for scan attacks
            else:
                colors.append("#8B0000")  # Dark red for other attacks
        
        fig = go.Figure(data=[
            go.Pie(
                labels=labels,
                values=values,
                hole=0.4,  # Donut chart
                marker=dict(colors=colors, line=dict(color='#FFFFFF', width=2)),
                textinfo='label+percent',
                textposition='outside'
            )
        ])
        
        fig.update_layout(
            title="Traffic Distribution",
            height=400,
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="middle",
                y=0.5,
                xanchor="left",
                x=1.05
            )
        )
        
        return fig
    
    def create_requests_over_time_chart(self):
        """Create requests over time chart (like in original project)"""
        results = self.state.get("current_results", [])
        
        if not results:
            fig = go.Figure()
            fig.add_annotation(text="No data to display", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            fig.update_layout(title="Total Requests Over Time", height=400)
            return fig
        
        # Prepare time series data
        timestamps = list(range(len(results)))
        cumulative_count = list(range(1, len(results) + 1))
        
        # Calculate blocked and allowed counts over time
        blocked_count = []
        allowed_count = []
        
        blocked_total = 0
        allowed_total = 0
        
        for result in results:
            if result.get("is_anomaly", False):
                blocked_total += 1
            else:
                allowed_total += 1
            
            blocked_count.append(blocked_total)
            allowed_count.append(allowed_total)
        
        fig = go.Figure()
        
        # Total requests line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=cumulative_count,
            mode='lines',
            name='Total Requests',
            line=dict(color='blue', width=2),
            showlegend=True
        ))
        
        # Allowed requests line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=allowed_count,
            mode='lines',
            name='Allowed Requests',
            line=dict(color='green', width=1),
            showlegend=True
        ))
        
        # Blocked requests line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=blocked_count,
            mode='lines',
            name='Blocked Requests',
            line=dict(color='red', width=1),
            showlegend=True
        ))
        
        fig.update_layout(
            title="Total Requests Over Time",
            xaxis_title="Record Index",
            yaxis_title="Request Count",
            height=400,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        return fig
    
    def create_throughput_chart(self):
        """Create throughput gauge chart"""
        stats = self.state.get("processing_stats", {})
        current_rate = stats.get("processing_rate", 0)
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = current_rate,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Records/Second"},
            delta = {'reference': 10},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 25], 'color': "lightgray"},
                    {'range': [25, 50], 'color': "gray"},
                    {'range': [50, 100], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ))
        
        fig.update_layout(height=400)
        return fig
    
    def get_recent_detections(self, limit: int = 10) -> str:
        """Get recent anomaly detections as formatted text"""
        results = self.state.get("current_results", [])
        
        if not results:
            return "No detections yet"
        
        recent_anomalies = [
            r for r in results[-50:] 
            if r.get("is_anomaly", False)
        ][-limit:]
        
        if not recent_anomalies:
            return "No recent anomalies detected"
        
        output = "Recent Anomaly Detections:\n\n"
        for i, detection in enumerate(recent_anomalies, 1):
            output += f"{i}. Attack Type: {detection.get('attack_type', 'Unknown')}\n"
            output += f"   Confidence: {detection.get('binary_confidence', 0):.3f}\n"
            output += f"   Processing Time: {detection.get('total_processing_time_ms', 0):.1f}ms\n\n"
        
        return output
    
    def get_logs(self) -> str:
        """Get recent log messages"""
        logs = self.state.get("log_messages", [])
        return "\n".join(logs[-20:]) if logs else "No logs available"


def create_streamlit_dashboard():
    """Create the main Streamlit dashboard with efficient updates"""
    st.set_page_config(
        page_title="5G Security Analytics Dashboard",
        page_icon="🛡️",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize dashboard
    dashboard = StreamlitDashboard()

    # Initialize session state
    if 'last_refresh' not in st.session_state:
        st.session_state.last_refresh = time.time()
    if 'auto_refresh_enabled' not in st.session_state:
        st.session_state.auto_refresh_enabled = True

    # Header
    st.title("🛡️ 5G Security Analytics - Real-time Monitor")
    st.markdown("Live monitoring dashboard for 5G network security analysis")

    # Auto-refresh controls
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        auto_refresh = st.checkbox("Auto-refresh", value=st.session_state.auto_refresh_enabled)
        st.session_state.auto_refresh_enabled = auto_refresh
    with col2:
        refresh_interval = st.selectbox("Interval", [1, 2, 3, 5], index=1, format_func=lambda x: f"{x}s")
    with col3:
        if st.button("🔄 Manual Refresh", key=dashboard.get_unique_key("manual_refresh")):
            st.rerun()

    # Status section
    st.markdown("---")
    col1, col2, col3 = st.columns(3)

    is_processing = dashboard.state.get("is_processing", False)
    dataset_loaded = len(dashboard.state.get("current_results", [])) > 0

    with col1:
        st.metric("Dataset Status", "✅ Loaded" if dataset_loaded else "❌ No Data")
    with col2:
        st.metric("Model Status", "✅ Ready")
    with col3:
        st.metric("Processing Status", "🔄 Active" if is_processing else "⏸️ Ready")

    # Main metrics
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)

    stats = dashboard.state.get("processing_stats", {})

    with col1:
        st.metric("Total Processed", f"{stats.get('total_processed', 0):,}")
    with col2:
        st.metric("Total Anomalies", f"{stats.get('total_anomalies', 0):,}")
    with col3:
        anomaly_rate = 0
        if stats.get('total_processed', 0) > 0:
            anomaly_rate = (stats.get('total_anomalies', 0) / stats.get('total_processed', 0)) * 100
        st.metric("Anomaly Rate", f"{anomaly_rate:.1f}%")
    with col4:
        st.metric("Processing Rate", f"{stats.get('processing_rate', 0):.1f} rec/sec")

    # Charts section
    st.markdown("---")

    # Row 1: Timeline (larger) and Traffic Distribution (smaller)
    col1, col2 = st.columns([3, 2])

    with col1:
        st.subheader("Anomaly Detection Timeline")
        timeline_chart = dashboard.create_anomaly_timeline_chart()
        st.plotly_chart(timeline_chart, use_container_width=True, key=dashboard.get_unique_key("timeline"))

    with col2:
        st.subheader("Traffic Distribution")
        pie_chart = dashboard.create_attack_types_pie_chart()
        st.plotly_chart(pie_chart, use_container_width=True, key=dashboard.get_unique_key("pie"))

    # Row 2: Requests Over Time and Processing Rate
    col1, col2 = st.columns([3, 2])

    with col1:
        st.subheader("Total Requests Over Time")
        requests_chart = dashboard.create_requests_over_time_chart()
        st.plotly_chart(requests_chart, use_container_width=True, key=dashboard.get_unique_key("requests"))

    with col2:
        st.subheader("Real-time Processing Rate")
        throughput_chart = dashboard.create_throughput_chart()
        st.plotly_chart(throughput_chart, use_container_width=True, key=dashboard.get_unique_key("throughput"))

    # Details section
    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Recent Detections")
        recent_detections = dashboard.get_recent_detections()
        st.text_area("Latest Anomalies", recent_detections, height=300, key=dashboard.get_unique_key("detections"))

    with col2:
        st.subheader("System Logs")
        logs = dashboard.get_logs()
        st.text_area("Processing Logs", logs, height=300, key=dashboard.get_unique_key("logs"))

    # Sidebar info
    st.sidebar.header("Dashboard Info")
    st.sidebar.info("This dashboard shows real-time monitoring of the 5G security analytics system.")
    st.sidebar.info("Use the Gradio control panel to start/stop processing and configure settings.")

    # Current settings display
    st.sidebar.markdown("### Current Settings")
    st.sidebar.text(f"Batch Size: {dashboard.state.get('batch_size', 100)}")
    st.sidebar.text(f"Max Batches: {dashboard.state.get('max_batches', 10)}")
    st.sidebar.text(f"RPS: {dashboard.state.get('requests_per_second', 10.0)}")
    st.sidebar.text(f"Mode: {dashboard.state.get('processing_mode', 'Manual')}")

    # Processing status indicator
    if is_processing:
        st.sidebar.success("🔄 Processing Active")
    else:
        st.sidebar.info("⏸️ Ready")

    # Auto-refresh logic
    current_time = time.time()

    if st.session_state.auto_refresh_enabled:
        # More frequent refresh when processing
        interval = 1 if is_processing else refresh_interval

        if current_time - st.session_state.last_refresh > interval:
            st.session_state.last_refresh = current_time
            time.sleep(0.1)  # Small delay to prevent too frequent updates
            st.rerun()

    # Manual refresh in sidebar
    if st.sidebar.button("🔄 Refresh Now", key=dashboard.get_unique_key("sidebar_refresh")):
        st.session_state.last_refresh = time.time()
        st.rerun()

    # Show last update time
    st.sidebar.caption(f"Last updated: {time.strftime('%H:%M:%S', time.localtime(st.session_state.last_refresh))}")


if __name__ == "__main__":
    create_streamlit_dashboard()
