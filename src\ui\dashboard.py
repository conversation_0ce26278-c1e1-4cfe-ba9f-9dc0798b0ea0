"""
Gradio Dashboard for 5G Security Analytics
"""

import gradio as gr
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import time
import threading
import logging
from typing import Dict, Any, List, Optional, Tuple
import json

from ..core.data_loader import DataLoader
from ..core.batch_processor import BatchProcessor
from ..models.anomaly_detector import AnomalyDetector
from ..utils.metrics import MetricsCollector, ThroughputCalculator

logger = logging.getLogger(__name__)


class SecurityDashboard:
    """
    Main dashboard class for 5G Security Analytics
    """
    
    def __init__(self, data_path: str = "data/sample_inputs/5g_nidd_dataset.csv",
                 models_path: str = "models/rf_models"):
        """
        Initialize the dashboard
        
        Args:
            data_path: Path to the dataset
            models_path: Path to the models directory
        """
        self.data_loader = DataLoader(data_path)
        self.detector = AnomalyDetector(models_path)
        self.batch_processor = BatchProcessor(self.detector)
        self.metrics = MetricsCollector()
        self.throughput_calc = ThroughputCalculator()
        
        # Processing state
        self.is_processing = False
        self.processing_thread = None
        self.current_results = []
        self.processing_stats = {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        }
        
        # UI state
        self.log_messages = []
        
    def add_log(self, message: str, level: str = "INFO"):
        """Add a log message"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_messages.append(f"[{timestamp}] {level}: {message}")
        if len(self.log_messages) > 100:  # Keep only last 100 messages
            self.log_messages.pop(0)
        logger.info(message)
    
    def get_system_status(self) -> Tuple[str, str, str, str]:
        """
        Get current system status
        
        Returns:
            Tuple of (dataset_status, model_status, processing_status, performance_info)
        """
        # Dataset status
        if self.data_loader.df is not None:
            dataset_info = self.data_loader.get_dataset_info()
            dataset_status = f"✅ Dataset loaded: {dataset_info['total_records']:,} records"
        else:
            dataset_status = "❌ Dataset not loaded"
        
        # Model status
        if (self.detector.binary_model is not None and 
            self.detector.multiclass_model is not None):
            model_status = "✅ Models loaded successfully"
        else:
            model_status = "❌ Models not loaded"
        
        # Processing status
        if self.is_processing:
            processing_status = f"🔄 Processing... Batch {self.processing_stats['current_batch']}"
        else:
            processing_status = "⏸️ Ready to process"
        
        # Performance info
        stats = self.batch_processor.get_stats()
        if stats["total_records"] > 0:
            performance_info = (
                f"📊 Processed: {stats['total_records']:,} records | "
                f"Anomalies: {stats['total_anomalies']:,} | "
                f"Rate: {stats['throughput_records_per_sec']:.1f} rec/sec"
            )
        else:
            performance_info = "📊 No processing statistics yet"
        
        return dataset_status, model_status, processing_status, performance_info
    
    def process_batch_sync(self, batch_size: int, max_batches: int, 
                          progress_callback=None) -> List[Dict[str, Any]]:
        """
        Process data in batches synchronously
        
        Args:
            batch_size: Size of each batch
            max_batches: Maximum number of batches to process
            progress_callback: Optional progress callback
            
        Returns:
            List of processing results
        """
        if self.data_loader.df is None:
            self.add_log("No dataset loaded", "ERROR")
            return []
        
        self.is_processing = True
        self.current_results = []
        self.processing_stats["current_batch"] = 0
        
        try:
            self.add_log(f"Starting batch processing: {batch_size} records/batch, max {max_batches} batches")
            
            for batch_num in range(max_batches):
                if not self.is_processing:  # Check for stop signal
                    break
                
                # Get batch data
                batch_df = self.data_loader.get_batch(batch_size)
                if batch_df is None or batch_df.empty:
                    self.add_log("No more data to process")
                    break
                
                self.processing_stats["current_batch"] = batch_num + 1
                
                # Process batch
                batch_data = batch_df.to_dict('records')
                batch_results = self.batch_processor.process_batch_sequential(batch_data)
                
                # Update results and stats
                self.current_results.extend(batch_results)
                anomaly_count = sum(1 for r in batch_results if r.get("is_anomaly", False))
                
                self.processing_stats["total_processed"] += len(batch_results)
                self.processing_stats["total_anomalies"] += anomaly_count
                
                # Update throughput
                for _ in batch_results:
                    self.throughput_calc.add_event()
                
                self.processing_stats["processing_rate"] = self.throughput_calc.get_throughput()
                
                self.add_log(f"Batch {batch_num + 1} completed: {len(batch_results)} records, {anomaly_count} anomalies")
                
                if progress_callback:
                    progress_callback(batch_num + 1, max_batches)
                
                # Small delay to prevent UI freezing
                time.sleep(0.1)
            
            self.add_log(f"Processing completed: {self.processing_stats['total_processed']} total records")
            
        except Exception as e:
            self.add_log(f"Error during processing: {e}", "ERROR")
        finally:
            self.is_processing = False
        
        return self.current_results
    
    def stop_processing(self):
        """Stop current processing"""
        self.is_processing = False
        self.add_log("Processing stopped by user")
    
    def get_results_summary(self) -> Dict[str, Any]:
        """Get summary of current results"""
        if not self.current_results:
            return {"message": "No results available"}
        
        total_records = len(self.current_results)
        anomalies = sum(1 for r in self.current_results if r.get("is_anomaly", False))
        
        # Count attack types
        attack_types = {}
        for result in self.current_results:
            if result.get("is_anomaly", False):
                attack_type = result.get("attack_type", "Unknown")
                attack_types[attack_type] = attack_types.get(attack_type, 0) + 1
        
        return {
            "total_records": total_records,
            "total_anomalies": anomalies,
            "anomaly_rate": (anomalies / total_records * 100) if total_records > 0 else 0,
            "attack_types": attack_types,
            "processing_stats": self.batch_processor.get_stats()
        }
    
    def create_anomaly_chart(self) -> go.Figure:
        """Create anomaly detection chart"""
        if not self.current_results:
            fig = go.Figure()
            fig.add_annotation(text="No data to display", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            return fig
        
        # Prepare data for plotting
        timestamps = list(range(len(self.current_results)))
        anomaly_scores = [r.get("binary_confidence", 0) for r in self.current_results]
        is_anomaly = [r.get("is_anomaly", False) for r in self.current_results]
        
        fig = go.Figure()
        
        # Add confidence score line
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=anomaly_scores,
            mode='lines',
            name='Anomaly Confidence',
            line=dict(color='blue', width=1)
        ))
        
        # Add anomaly markers
        anomaly_x = [i for i, is_anom in enumerate(is_anomaly) if is_anom]
        anomaly_y = [anomaly_scores[i] for i in anomaly_x]
        
        fig.add_trace(go.Scatter(
            x=anomaly_x,
            y=anomaly_y,
            mode='markers',
            name='Detected Anomalies',
            marker=dict(color='red', size=8, symbol='triangle-up')
        ))
        
        # Add threshold line
        fig.add_hline(y=0.3, line_dash="dash", line_color="orange", 
                     annotation_text="Detection Threshold")
        
        fig.update_layout(
            title="Anomaly Detection Results",
            xaxis_title="Record Index",
            yaxis_title="Anomaly Confidence",
            height=400
        )
        
        return fig
    
    def create_attack_types_chart(self) -> go.Figure:
        """Create attack types distribution chart"""
        summary = self.get_results_summary()
        attack_types = summary.get("attack_types", {})
        
        if not attack_types:
            fig = go.Figure()
            fig.add_annotation(text="No anomalies detected", 
                             xref="paper", yref="paper",
                             x=0.5, y=0.5, showarrow=False)
            return fig
        
        fig = go.Figure(data=[
            go.Bar(x=list(attack_types.keys()), 
                   y=list(attack_types.values()),
                   marker_color='red')
        ])
        
        fig.update_layout(
            title="Detected Attack Types",
            xaxis_title="Attack Type",
            yaxis_title="Count",
            height=400
        )
        
        return fig
    
    def get_recent_detections(self, limit: int = 10) -> str:
        """Get recent anomaly detections as formatted text"""
        if not self.current_results:
            return "No detections yet"
        
        recent_anomalies = [
            r for r in self.current_results[-50:] 
            if r.get("is_anomaly", False)
        ][-limit:]
        
        if not recent_anomalies:
            return "No recent anomalies detected"
        
        output = "Recent Anomaly Detections:\n\n"
        for i, detection in enumerate(recent_anomalies, 1):
            output += f"{i}. Attack Type: {detection.get('attack_type', 'Unknown')}\n"
            output += f"   Confidence: {detection.get('binary_confidence', 0):.3f}\n"
            output += f"   Processing Time: {detection.get('total_processing_time_ms', 0):.1f}ms\n\n"
        
        return output
    
    def get_logs(self) -> str:
        """Get recent log messages"""
        return "\n".join(self.log_messages[-20:])  # Last 20 messages


def create_dashboard(dashboard: SecurityDashboard) -> gr.Blocks:
    """
    Create the Gradio dashboard interface

    Args:
        dashboard: SecurityDashboard instance

    Returns:
        Gradio Blocks interface
    """

    with gr.Blocks(title="5G Security Analytics Dashboard", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🛡️ 5G Security Analytics Dashboard")
        gr.Markdown("Real-time anomaly detection for 5G network traffic using RandomForest models")

        # Status section
        with gr.Row():
            with gr.Column(scale=1):
                dataset_status = gr.Textbox(label="Dataset Status", interactive=False)
                model_status = gr.Textbox(label="Model Status", interactive=False)
            with gr.Column(scale=1):
                processing_status = gr.Textbox(label="Processing Status", interactive=False)
                performance_info = gr.Textbox(label="Performance Info", interactive=False)

        # Control section
        with gr.Row():
            with gr.Column(scale=2):
                gr.Markdown("### Processing Controls")
                with gr.Row():
                    batch_size = gr.Slider(
                        minimum=10, maximum=1000, value=100, step=10,
                        label="Batch Size", info="Number of records per batch"
                    )
                    max_batches = gr.Slider(
                        minimum=1, maximum=100, value=10, step=1,
                        label="Max Batches", info="Maximum number of batches to process"
                    )

                with gr.Row():
                    start_btn = gr.Button("🚀 Start Processing", variant="primary", size="lg")
                    stop_btn = gr.Button("⏹️ Stop Processing", variant="stop", size="lg")
                    refresh_btn = gr.Button("🔄 Refresh Status", size="lg")

            with gr.Column(scale=1):
                gr.Markdown("### Quick Stats")
                total_processed = gr.Number(label="Total Processed", value=0, interactive=False)
                total_anomalies = gr.Number(label="Total Anomalies", value=0, interactive=False)
                anomaly_rate = gr.Number(label="Anomaly Rate (%)", value=0, interactive=False)

        # Charts section
        with gr.Row():
            with gr.Column():
                anomaly_chart = gr.Plot(label="Anomaly Detection Timeline")
            with gr.Column():
                attack_types_chart = gr.Plot(label="Attack Types Distribution")

        # Results and logs section
        with gr.Row():
            with gr.Column():
                gr.Markdown("### Recent Detections")
                recent_detections = gr.Textbox(
                    label="Latest Anomalies",
                    lines=10,
                    max_lines=15,
                    interactive=False
                )
            with gr.Column():
                gr.Markdown("### System Logs")
                logs_display = gr.Textbox(
                    label="Recent Logs",
                    lines=10,
                    max_lines=15,
                    interactive=False
                )

        # Processing progress
        processing_progress = gr.Progress()

        def update_status():
            """Update all status displays"""
            ds_status, m_status, p_status, perf_info = dashboard.get_system_status()
            summary = dashboard.get_results_summary()

            return (
                ds_status, m_status, p_status, perf_info,
                summary.get("total_records", 0),
                summary.get("total_anomalies", 0),
                round(summary.get("anomaly_rate", 0), 2)
            )

        def start_processing(batch_size_val, max_batches_val, progress=gr.Progress()):
            """Start batch processing"""
            if dashboard.is_processing:
                return update_status() + (
                    dashboard.create_anomaly_chart(),
                    dashboard.create_attack_types_chart(),
                    dashboard.get_recent_detections(),
                    dashboard.get_logs()
                )

            def progress_callback(current, total):
                progress((current, total), desc=f"Processing batch {current}/{total}")

            # Start processing
            results = dashboard.process_batch_sync(
                int(batch_size_val),
                int(max_batches_val),
                progress_callback
            )

            # Update all displays
            status_updates = update_status()
            charts = (
                dashboard.create_anomaly_chart(),
                dashboard.create_attack_types_chart()
            )
            text_updates = (
                dashboard.get_recent_detections(),
                dashboard.get_logs()
            )

            return status_updates + charts + text_updates

        def stop_processing():
            """Stop processing"""
            dashboard.stop_processing()
            return update_status() + (
                dashboard.create_anomaly_chart(),
                dashboard.create_attack_types_chart(),
                dashboard.get_recent_detections(),
                dashboard.get_logs()
            )

        def refresh_status():
            """Refresh all displays"""
            return update_status() + (
                dashboard.create_anomaly_chart(),
                dashboard.create_attack_types_chart(),
                dashboard.get_recent_detections(),
                dashboard.get_logs()
            )

        # Event handlers
        start_btn.click(
            fn=start_processing,
            inputs=[batch_size, max_batches],
            outputs=[
                dataset_status, model_status, processing_status, performance_info,
                total_processed, total_anomalies, anomaly_rate,
                anomaly_chart, attack_types_chart,
                recent_detections, logs_display
            ]
        )

        stop_btn.click(
            fn=stop_processing,
            outputs=[
                dataset_status, model_status, processing_status, performance_info,
                total_processed, total_anomalies, anomaly_rate,
                anomaly_chart, attack_types_chart,
                recent_detections, logs_display
            ]
        )

        refresh_btn.click(
            fn=refresh_status,
            outputs=[
                dataset_status, model_status, processing_status, performance_info,
                total_processed, total_anomalies, anomaly_rate,
                anomaly_chart, attack_types_chart,
                recent_detections, logs_display
            ]
        )

        # Auto-refresh every 5 seconds when processing
        demo.load(
            fn=refresh_status,
            outputs=[
                dataset_status, model_status, processing_status, performance_info,
                total_processed, total_anomalies, anomaly_rate,
                anomaly_chart, attack_types_chart,
                recent_detections, logs_display
            ]
        )

    return demo
