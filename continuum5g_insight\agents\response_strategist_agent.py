# Continuum5G Insight - Adaptive Response Strategist Agent
# Uses NeMo SLM with RAG to formulate response strategies.

import json
import os
import datetime 
import time 
import logging
from datetime import datetime # Ensure datetime is imported

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

# --- IMPORTANT USER REMINDER ---
# (User reminders via print are kept for direct script execution visibility)
print("------------------------------------------------------------------------------------")
print("IMPORTANT KNOWLEDGE BASE PATH CONFIGURATION (ResponseStrategistAgent):")
print("The KB_PATH_RESPONSE_PLAYBOOKS path below is a placeholder.")
print("It's currently set relative to this agent's file location for initial setup.")
KB_PATH_RESPONSE_PLAYBOOKS = "../knowledge_bases/response_playbooks_kb.json" # Relative to agents/
abs_kb_path = os.path.abspath(os.path.join(os.path.dirname(__file__), KB_PATH_RESPONSE_PLAYBOOKS))
print(f"Response Playbooks KB Path (resolved by ResponseStrategistAgent): {abs_kb_path}")
print("------------------------------------------------------------------------------------")


def load_knowledge_base(kb_path):
    """
    Loads a knowledge base from the given JSON file path.
    Path is assumed to be relative to this script's directory if not absolute.
    """
    script_dir = os.path.dirname(__file__)
    resolved_kb_path = os.path.abspath(os.path.join(script_dir, kb_path))
    logger.info(f"Attempting to load knowledge base from resolved path: {resolved_kb_path}")

    if not os.path.exists(resolved_kb_path):
        logger.error(f"Knowledge base file not found at {resolved_kb_path}.")
        return {}
            
    try:
        with open(resolved_kb_path, 'r') as f:
            kb = json.load(f)
        logger.info(f"Successfully loaded knowledge base from: {resolved_kb_path}. Contains {len(kb)} entries.")
        return kb
    except json.JSONDecodeError as e:
        logger.error(f"Could not decode JSON from knowledge base file {resolved_kb_path}. Error: {e}", exc_info=True)
        return {}
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading KB {resolved_kb_path}: {e}", exc_info=True)
        return {}

def simulate_slm_call(prompt, service_name="ResponseStrategy", knowledge_base=None, enriched_alert_data=None):
    """
    Simulates a call to an SLM (e.g., served via NVIDIA NIM) for response strategy generation.
    Now incorporates basic RAG by looking up relevant playbooks in the knowledge_base.
    """
    logger.info(f"--- SIMULATING SLM Call for {service_name} ---")
    logger.debug(f"PROMPT for {service_name} SLM:\\n{prompt}\\n--- END PROMPT ---")

    response = {
        "suggested_response_plan": {
            "name": "Default Fallback Plan",
            "steps": [{"action": "Log the alert and monitor further.", "justification": "No specific playbook matched."}],
            "confidence": "Low"
        },
        "rag_references": []
    }

    if knowledge_base is None:
        knowledge_base = {}
    if enriched_alert_data is None:
        enriched_alert_data = {}
    
    playbooks = knowledge_base.get("playbooks", [])
    multiclass_pred = enriched_alert_data.get("multiclass_prediction")
    priority = enriched_alert_data.get("priority_assessment")
    is_binary_anomaly = enriched_alert_data.get("binary_prediction") == 1 and (multiclass_pred is None or multiclass_pred == "Benign" or multiclass_pred == CLASS_LABELS_MAP.get(0))


    matched_playbook = None
    for playbook in playbooks:
        conditions = playbook.get("conditions", {})
        match = False
        
        if "multiclass_prediction_contains" in conditions and multiclass_pred:
            if any(pred_type in multiclass_pred for pred_type in conditions["multiclass_prediction_contains"]):
                match = True
        elif "is_binary_anomaly_only" in conditions and conditions["is_binary_anomaly_only"] and is_binary_anomaly:
            match = True
        else: # If no multiclass condition, it might be a general playbook or binary only
            if not multiclass_pred and "is_binary_anomaly_only" not in conditions and "multiclass_prediction_contains" not in conditions:
                 # This case is tricky, could be a default playbook. For now, require specific conditions.
                 pass

        if match and "priority_assessment_is" in conditions and priority:
            if priority not in conditions["priority_assessment_is"]:
                match = False # Priority condition not met
        elif match and "priority_assessment_is" in conditions and not priority: # Priority condition exists but no priority in alert
             match = False


        if match:
            matched_playbook = playbook
            break # Use the first matching playbook

    if matched_playbook:
        response["suggested_response_plan"]["name"] = matched_playbook.get("name", f"Playbook for {multiclass_pred or 'Binary Anomaly'}")
        response["suggested_response_plan"]["steps"] = matched_playbook.get("response_steps", [])
        response["suggested_response_plan"]["confidence"] = "High" # Assuming KB match is high confidence
        response["rag_references"].append({
            "source": "KB:response_playbooks", 
            "playbook_conditions": str(matched_playbook.get("conditions"))
        })
    
    # Simulate SLM processing time
    time.sleep(0.05)
    
    logger.debug(f"SIMULATED SLM RESPONSE for {service_name}:\\n{json.dumps(response, indent=2)}\\n--- END SIMULATED SLM Call ---")
    return response


class ResponseStrategistAgent:
    def __init__(self, kb_path=KB_PATH_RESPONSE_PLAYBOOKS):
        logger.info("Initializing ResponseStrategistAgent...")
        self.knowledge_base = load_knowledge_base(kb_path)
        if not self.knowledge_base:
            logger.warning(f"Response playbooks knowledge base failed to load from {kb_path} or is empty. RAG capabilities will be limited.")
        else:
            logger.info(f"Response playbooks knowledge base loaded successfully with {len(self.knowledge_base.get('playbooks',[]))} playbooks.")
        # self.nemo_client = SomeNemoNIMClient() # Placeholder
        logger.info("ResponseStrategistAgent initialized.")

    def generate_response_strategy(self, enriched_alert):
        """
        Generates a response strategy based on the enriched alert from ThreatAnalysisAgent.
        enriched_alert is the output from ThreatAnalysisAgent.
        """
        logger.debug(f"Generating response strategy for enriched alert: {enriched_alert.get('analysis_summary')}")

        prompt = self._build_slm_prompt(enriched_alert)
        
        slm_response_suggestion = simulate_slm_call(
            prompt,
            service_name="ResponseStrategyRAG",
            knowledge_base=self.knowledge_base,
            enriched_alert_data=enriched_alert
        )

        final_response = {
            "based_on_analysis": {
                "summary": enriched_alert.get("analysis_summary"),
                "priority": enriched_alert.get("priority_assessment"),
                "original_detection_type": enriched_alert.get("multiclass_prediction") or ("Binary Anomaly" if enriched_alert.get("binary_prediction") == 1 else "Benign/Unknown"),
                "original_record_preview": enriched_alert.get("original_record", {}).get("saddr", "N/A") # Example field
            },
            "suggested_plan": slm_response_suggestion.get("suggested_response_plan"),
            "rag_references": slm_response_suggestion.get("rag_references", []),
            "timestamp": datetime.now().isoformat(),
            "agent_notes": ["Response strategy generated using SLM+RAG simulation."]
        }
        logger.info(f"Response strategy generated: {final_response['suggested_plan'].get('name', 'N/A') if final_response['suggested_plan'] else 'N/A'}")
        return final_response

    def _build_slm_prompt(self, enriched_alert):
        """
        Helper function to build a detailed prompt for the SLM.
        """
        try:
            alert_analysis_details = json.dumps(enriched_alert, default=str)
        except Exception as e:
            logger.error(f"Error serializing enriched_alert for prompt: {e}")
            alert_analysis_details = str(enriched_alert)

        prompt = f"""Based on the following threat analysis, generate a specific and actionable response plan.
        Consult our knowledge base of response playbooks to find the most relevant set of actions.

        Enriched Threat Alert Data:
        {alert_analysis_details}

        Consider:
        1. The assessed priority of the threat.
        2. The type of attack or anomaly detected.
        3. The context provided by the Threat Analysis Agent.
        4. Specific steps from relevant playbooks in the knowledge base.

        Provide your response plan in JSON format with keys: "suggested_response_plan" (containing "name", "steps", "confidence"), and "rag_references".
        Each step in the plan should include "action", "description", "priority_order", and "justification".
        """
        return prompt

# Adding CLASS_LABELS_MAP here for completeness if it's needed by the modified simulate_slm_call
# This is a common map and might be better placed in a shared utils or constants file.
CLASS_LABELS_MAP = {
    0: 'Benign', 1: 'UDPFlood', 2: 'HTTPFlood', 3: 'SlowrateDoS',
    4: 'TCPConnectScan', 5: 'SYNScan', 6: 'UDPScan',
    7: 'SYNFlood', 8: 'ICMPFlood'
}

if __name__ == '__main__':
    # Basic logging setup for direct testing of this script
    import sys # Ensure sys is imported for direct run logging
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', stream=sys.stdout)
    logger.info("--- Test Response Strategist Agent (Direct Run with Logging, Simulated SLM) ---")

    sample_analyzed_alert_portscan = {
        "original_alert_details": {
            'original_record': {'src_ip': '********', 'dst_ip': '*************', 'dst_port': 80, 'protocol': 'TCP', 'flow_duration': 60, 'byte_count': 1500},
            'binary_prediction': 1,
            'binary_proba': [0.1, 0.9],
            'multiclass_prediction': 'PortScan', 
            'multiclass_proba': [0.1, 0.05, 0.8, 0.05], 
            'timestamp': datetime.datetime.now().isoformat(),
            'agent_notes': 'Detection successful.'
        },
        "slm_analysis": { 
            "summary": "Simulated SLM: Potential reconnaissance activity detected: Port scan. This is often a precursor to targeted attacks.",
            "confidence_score": 0.85,
            "threat_level_assessment": "Medium", 
            "recommended_actions_summary": ["Monitor source IP activity", "Verify firewall configurations"],
            "correlation_hypotheses": ["Could be related to recent phishing campaigns."]
        },
        'analysis_timestamp': datetime.datetime.now().isoformat(),
        'correlation_id_analysis': 'corr_ta_12345abc' # Use the specific correlation ID from threat analysis
    }

    sample_analyzed_alert_ddos = {
        "original_alert_details": {
            'original_record': {'src_ip': '*******', 'dst_ip': '*************', 'dst_port': 53, 'protocol': 'UDP', 'packet_count': 10000, 'byte_count': 5000000},
            'binary_prediction': 1,
            'binary_proba': [0.05, 0.95],
            'multiclass_prediction': 'DDoS_Attempt',
            'multiclass_proba': [0.92, 0.03, 0.05],
            'timestamp': datetime.datetime.now().isoformat(),
            'agent_notes': 'Detection successful, high volume UDP traffic.'
        },
        "slm_analysis": {
            "summary": "Simulated SLM: High volume of traffic consistent with a DDoS attempt.",
            "confidence_score": 0.90,
            "threat_level_assessment": "High", 
            "recommended_actions_summary": ["Activate DDoS mitigation protocols."],
            "correlation_hypotheses": ["May be part of a larger campaign."]
        },
        'analysis_timestamp': datetime.datetime.now().isoformat(),
        'correlation_id_analysis': 'corr_ta_67890def'
    }
    
    kb_file_path_for_test = KB_PATH_RESPONSE_PLAYBOOKS
    
    test_response_kb_content = {
        "PortScan": {
            "playbook_name": "Port Scan Response Playbook (for RAG context)",
            "initial_actions": ["Log source IP.", "Identify target assets.", "Check source reputation."],
            "containment_steps": ["Consider temporary block if aggressive.", "Monitor for follow-up activity."],
            "severity_guidance": "Usually Medium, escalate if critical assets targeted or repeated."
        },
        "DDoS_Attempt": { 
            "playbook_name": "DDoS Mitigation Playbook (for RAG context)",
            "initial_actions": ["Confirm attack with multiple sources.", "Assess service impact."],
            "containment_steps": ["Engage DDoS mitigation provider.", "Apply rate limiting/blacklisting."],
            "severity_guidance": "Typically High to Critical. Immediate action required."
        }
    }
    
    script_dir_for_test = os.path.dirname(__file__)
    resolved_kb_file_path_for_test = os.path.abspath(os.path.join(script_dir_for_test, kb_file_path_for_test))
    kb_response_dir_for_test = os.path.dirname(resolved_kb_file_path_for_test)

    if not os.path.exists(kb_response_dir_for_test):
        try:
            os.makedirs(kb_response_dir_for_test)
            logger.info(f"Test: Created directory for Response KB: {kb_response_dir_for_test}")
        except OSError as e:
            logger.error(f"Test: Error creating directory for Response KB '{kb_response_dir_for_test}': {e}")

    if os.path.exists(kb_response_dir_for_test):
        try:
            with open(resolved_kb_file_path_for_test, 'w') as f:
                json.dump(test_response_kb_content, f, indent=4)
            logger.info(f"Test: Populated placeholder Response KB at '{resolved_kb_file_path_for_test}' for testing.")
        except IOError as e:
            logger.error(f"Test: Error writing placeholder Response KB for test: {e}")
    else:
        logger.warning(f"Test: Skipping Response KB population as directory '{kb_response_dir_for_test}' could not be confirmed.")

    agent = ResponseStrategistAgent(kb_path_response_playbooks=KB_PATH_RESPONSE_PLAYBOOKS)

    if not agent.response_playbooks_kb:
        logger.warning("Test: Response Playbooks KB is empty or failed to load. RAG context for SLM prompts will be limited.")

    logger.info("\n--- Generating Response Strategy for 'PortScan' (Simulated SLM) ---")
    response_output_1 = agent.generate_response_strategy(sample_analyzed_alert_portscan)
    logger.info(f"\nFinal Response Output 1: {json.dumps(response_output_1, indent=2, default=str)}")
    
    logger.info("\n--- Generating Response Strategy for 'DDoS_Attempt' (Simulated SLM) ---")
    response_output_ddos = agent.generate_response_strategy(sample_analyzed_alert_ddos)
    logger.info(f"\nFinal Response Output DDoS: {json.dumps(response_output_ddos, indent=2, default=str)}")
    
    logger.info("\n--- Finished Response Strategist Agent Test (Direct Run with Logging, Simulated SLM) ---")

    try:
        if os.path.exists(resolved_kb_file_path_for_test):
            logger.info(f"Test KB file at '{resolved_kb_file_path_for_test}' can be manually removed if desired.")
    except Exception as e:
        logger.error(f"Error cleaning up test KB file: {e}", exc_info=True)

