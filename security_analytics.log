2025-06-09 18:26:22,151 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 18:26:23,476 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 18:26:24,630 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:26:24,630 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:26:24,690 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:26:24,690 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:26:24,690 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 18:26:25,655 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 18:26:25,785 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 18:26:26,310 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 18:27:43,748 - src.ui.dashboard - INFO - Starting batch processing: 100 records/batch, max 10 batches
2025-06-09 18:27:49,920 - src.ui.dashboard - INFO - Batch 1 completed: 100 records, 100 anomalies
2025-06-09 18:27:55,991 - src.ui.dashboard - INFO - Batch 2 completed: 100 records, 100 anomalies
2025-06-09 18:28:01,863 - src.ui.dashboard - INFO - Batch 3 completed: 100 records, 100 anomalies
2025-06-09 18:28:08,238 - src.ui.dashboard - INFO - Batch 4 completed: 100 records, 100 anomalies
2025-06-09 18:28:12,574 - src.ui.dashboard - INFO - Batch 5 completed: 100 records, 100 anomalies
2025-06-09 18:28:17,673 - src.ui.dashboard - INFO - Batch 6 completed: 100 records, 100 anomalies
2025-06-09 18:28:23,634 - src.ui.dashboard - INFO - Batch 7 completed: 100 records, 100 anomalies
2025-06-09 18:28:29,475 - src.ui.dashboard - INFO - Batch 8 completed: 100 records, 100 anomalies
2025-06-09 18:28:35,484 - src.ui.dashboard - INFO - Batch 9 completed: 100 records, 100 anomalies
2025-06-09 18:28:41,429 - src.ui.dashboard - INFO - Batch 10 completed: 100 records, 100 anomalies
2025-06-09 18:28:41,530 - src.ui.dashboard - INFO - Processing completed: 1000 total records
2025-06-09 18:36:02,300 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 18:36:03,497 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 18:36:03,497 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 18:36:04,370 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:36:04,370 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:36:04,413 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:36:04,414 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:36:04,414 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 18:36:04,415 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 18:36:05,020 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 18:36:05,047 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 18:36:05,864 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 19:10:25,999 - src.ui.control_panel - INFO - Starting processing: 100 records/batch, 10 batches max
2025-06-09 19:10:33,879 - src.ui.control_panel - INFO - Batch 1 completed: 100 records, 100 anomalies
2025-06-09 19:10:52,007 - src.ui.control_panel - INFO - Batch 2 completed: 100 records, 100 anomalies
2025-06-09 19:10:54,048 - src.ui.control_panel - INFO - Results and statistics cleared
2025-06-09 19:10:55,231 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:00,647 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:06,420 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:07,924 - src.ui.control_panel - INFO - Batch 3 completed: 100 records, 100 anomalies
2025-06-09 19:11:10,441 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:23,597 - src.ui.control_panel - INFO - Batch 4 completed: 100 records, 100 anomalies
2025-06-09 19:11:37,612 - src.ui.control_panel - INFO - Batch 5 completed: 100 records, 100 anomalies
2025-06-09 19:11:51,739 - src.ui.control_panel - INFO - Batch 6 completed: 100 records, 100 anomalies
2025-06-09 19:12:06,007 - src.ui.control_panel - INFO - Batch 7 completed: 100 records, 100 anomalies
2025-06-09 19:12:20,127 - src.ui.control_panel - INFO - Batch 8 completed: 100 records, 100 anomalies
2025-06-09 19:12:34,388 - src.ui.control_panel - INFO - Batch 9 completed: 100 records, 100 anomalies
2025-06-09 19:12:48,990 - src.ui.control_panel - INFO - Batch 10 completed: 100 records, 100 anomalies
2025-06-09 19:12:59,079 - src.ui.control_panel - INFO - Processing completed: 800 total records
2025-06-09 19:16:22,200 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:16:24,005 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:16:24,068 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:16:25,125 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:16:25,125 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:16:25,189 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:16:25,191 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:16:25,191 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:16:25,235 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:16:25,235 - __main__ - ERROR - Error starting control panel: cannot access local variable 'gr' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\app.py", line 59, in main
    demo = create_control_panel()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\src\ui\control_panel.py", line 285, in create_control_panel
    with gr.Blocks(title="5G Security Analytics - Control Panel", theme=gr.themes.Soft()) as demo:
         ^^
UnboundLocalError: cannot access local variable 'gr' where it is not associated with a value
2025-06-09 19:16:40,528 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:16:42,044 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:16:42,100 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:16:43,131 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:16:43,131 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:16:43,185 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:16:43,185 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:16:43,185 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:16:43,274 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:16:43,275 - __main__ - ERROR - Error starting control panel: cannot access local variable 'gr' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\app.py", line 59, in main
    demo = create_control_panel()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\src\ui\control_panel.py", line 285, in create_control_panel
    with gr.Blocks(title="5G Security Analytics - Control Panel", theme=gr.themes.Soft()) as demo:
         ^^
UnboundLocalError: cannot access local variable 'gr' where it is not associated with a value
2025-06-09 19:17:15,831 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:17:17,063 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:17:17,117 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:17:18,012 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:17:18,012 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:17:18,053 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:17:18,055 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:17:18,055 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:17:18,103 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:17:18,933 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 19:17:18,963 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 19:17:19,813 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
