2025-06-09 18:26:22,151 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 18:26:23,476 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 18:26:24,630 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:26:24,630 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:26:24,690 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:26:24,690 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:26:24,690 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 18:26:25,655 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 18:26:25,785 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 18:26:26,310 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 18:27:43,748 - src.ui.dashboard - INFO - Starting batch processing: 100 records/batch, max 10 batches
2025-06-09 18:27:49,920 - src.ui.dashboard - INFO - Batch 1 completed: 100 records, 100 anomalies
2025-06-09 18:27:55,991 - src.ui.dashboard - INFO - Batch 2 completed: 100 records, 100 anomalies
2025-06-09 18:28:01,863 - src.ui.dashboard - INFO - Batch 3 completed: 100 records, 100 anomalies
2025-06-09 18:28:08,238 - src.ui.dashboard - INFO - Batch 4 completed: 100 records, 100 anomalies
2025-06-09 18:28:12,574 - src.ui.dashboard - INFO - Batch 5 completed: 100 records, 100 anomalies
2025-06-09 18:28:17,673 - src.ui.dashboard - INFO - Batch 6 completed: 100 records, 100 anomalies
2025-06-09 18:28:23,634 - src.ui.dashboard - INFO - Batch 7 completed: 100 records, 100 anomalies
2025-06-09 18:28:29,475 - src.ui.dashboard - INFO - Batch 8 completed: 100 records, 100 anomalies
2025-06-09 18:28:35,484 - src.ui.dashboard - INFO - Batch 9 completed: 100 records, 100 anomalies
2025-06-09 18:28:41,429 - src.ui.dashboard - INFO - Batch 10 completed: 100 records, 100 anomalies
2025-06-09 18:28:41,530 - src.ui.dashboard - INFO - Processing completed: 1000 total records
2025-06-09 18:36:02,300 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 18:36:03,497 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 18:36:03,497 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 18:36:04,370 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:36:04,370 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:36:04,413 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:36:04,414 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:36:04,414 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 18:36:04,415 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 18:36:05,020 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 18:36:05,047 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 18:36:05,864 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 19:10:25,999 - src.ui.control_panel - INFO - Starting processing: 100 records/batch, 10 batches max
2025-06-09 19:10:33,879 - src.ui.control_panel - INFO - Batch 1 completed: 100 records, 100 anomalies
2025-06-09 19:10:52,007 - src.ui.control_panel - INFO - Batch 2 completed: 100 records, 100 anomalies
2025-06-09 19:10:54,048 - src.ui.control_panel - INFO - Results and statistics cleared
2025-06-09 19:10:55,231 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:00,647 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:06,420 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:07,924 - src.ui.control_panel - INFO - Batch 3 completed: 100 records, 100 anomalies
2025-06-09 19:11:10,441 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:11:23,597 - src.ui.control_panel - INFO - Batch 4 completed: 100 records, 100 anomalies
2025-06-09 19:11:37,612 - src.ui.control_panel - INFO - Batch 5 completed: 100 records, 100 anomalies
2025-06-09 19:11:51,739 - src.ui.control_panel - INFO - Batch 6 completed: 100 records, 100 anomalies
2025-06-09 19:12:06,007 - src.ui.control_panel - INFO - Batch 7 completed: 100 records, 100 anomalies
2025-06-09 19:12:20,127 - src.ui.control_panel - INFO - Batch 8 completed: 100 records, 100 anomalies
2025-06-09 19:12:34,388 - src.ui.control_panel - INFO - Batch 9 completed: 100 records, 100 anomalies
2025-06-09 19:12:48,990 - src.ui.control_panel - INFO - Batch 10 completed: 100 records, 100 anomalies
2025-06-09 19:12:59,079 - src.ui.control_panel - INFO - Processing completed: 800 total records
2025-06-09 19:16:22,200 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:16:24,005 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:16:24,068 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:16:25,125 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:16:25,125 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:16:25,189 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:16:25,191 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:16:25,191 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:16:25,235 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:16:25,235 - __main__ - ERROR - Error starting control panel: cannot access local variable 'gr' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\app.py", line 59, in main
    demo = create_control_panel()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\src\ui\control_panel.py", line 285, in create_control_panel
    with gr.Blocks(title="5G Security Analytics - Control Panel", theme=gr.themes.Soft()) as demo:
         ^^
UnboundLocalError: cannot access local variable 'gr' where it is not associated with a value
2025-06-09 19:16:40,528 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:16:42,044 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:16:42,100 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:16:43,131 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:16:43,131 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:16:43,185 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:16:43,185 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:16:43,185 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:16:43,274 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:16:43,275 - __main__ - ERROR - Error starting control panel: cannot access local variable 'gr' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\app.py", line 59, in main
    demo = create_control_panel()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\hg-hackathon25-track3\src\ui\control_panel.py", line 285, in create_control_panel
    with gr.Blocks(title="5G Security Analytics - Control Panel", theme=gr.themes.Soft()) as demo:
         ^^
UnboundLocalError: cannot access local variable 'gr' where it is not associated with a value
2025-06-09 19:17:15,831 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:17:17,063 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:17:17,117 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:17:18,012 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:17:18,012 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:17:18,053 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:17:18,055 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:17:18,055 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:17:18,103 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:17:18,933 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 19:17:18,963 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 19:17:19,813 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 19:18:15,893 - src.ui.control_panel - INFO - Starting processing: 100 records/batch, 10 batches max
2025-06-09 19:18:15,908 - src.ui.control_panel - INFO - Processing thread started successfully
2025-06-09 19:18:23,036 - src.ui.control_panel - INFO - Batch 1 completed: 100 records, 100 anomalies
2025-06-09 19:18:31,472 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:18:39,101 - src.ui.control_panel - INFO - Batch 2 completed: 100 records, 100 anomalies
2025-06-09 19:18:49,130 - src.ui.control_panel - INFO - Processing completed: 0 total records
2025-06-09 19:23:49,531 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:23:51,457 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:23:51,459 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:23:53,046 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:23:53,048 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:23:53,109 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:23:53,111 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:23:53,111 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:23:53,112 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:23:54,224 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 19:23:54,260 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 19:23:54,827 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 19:26:00,149 - src.ui.control_panel - INFO - Starting processing: 100 records/batch, 10 batches max
2025-06-09 19:26:00,172 - src.ui.control_panel - INFO - Processing thread started successfully
2025-06-09 19:26:06,494 - src.ui.control_panel - INFO - Batch 1 completed: 100 records, 100 anomalies
2025-06-09 19:26:22,323 - src.ui.control_panel - INFO - Batch 2 completed: 100 records, 100 anomalies
2025-06-09 19:26:38,412 - src.ui.control_panel - INFO - Batch 3 completed: 100 records, 100 anomalies
2025-06-09 19:26:54,473 - src.ui.control_panel - INFO - Batch 4 completed: 100 records, 100 anomalies
2025-06-09 19:26:54,541 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:27:10,334 - src.ui.control_panel - INFO - Batch 5 completed: 100 records, 100 anomalies
2025-06-09 19:27:26,578 - src.ui.control_panel - INFO - Batch 6 completed: 100 records, 100 anomalies
2025-06-09 19:27:40,781 - src.ui.control_panel - INFO - Batch 7 completed: 100 records, 100 anomalies
2025-06-09 19:27:54,937 - src.ui.control_panel - INFO - Batch 8 completed: 100 records, 100 anomalies
2025-06-09 19:28:09,286 - src.ui.control_panel - INFO - Batch 9 completed: 100 records, 100 anomalies
2025-06-09 19:28:23,668 - src.ui.control_panel - INFO - Batch 10 completed: 100 records, 100 anomalies
2025-06-09 19:28:33,762 - src.ui.control_panel - INFO - Processing completed: 1000 total records
2025-06-09 19:36:36,551 - src.ui.control_panel - INFO - Starting processing: 1000 records/batch, 100 batches max
2025-06-09 19:36:36,570 - src.ui.control_panel - INFO - Processing thread started successfully
2025-06-09 19:37:44,546 - src.ui.control_panel - INFO - Batch 1 completed: 1000 records, 1000 anomalies
2025-06-09 19:38:53,578 - src.ui.control_panel - INFO - Batch 2 completed: 1000 records, 999 anomalies
2025-06-09 19:40:04,536 - src.ui.control_panel - INFO - Batch 3 completed: 1000 records, 1000 anomalies
2025-06-09 19:41:14,550 - src.ui.control_panel - INFO - Batch 4 completed: 1000 records, 1000 anomalies
2025-06-09 19:42:30,634 - src.ui.control_panel - INFO - Batch 5 completed: 1000 records, 1000 anomalies
2025-06-09 19:43:01,271 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:43:12,620 - src.ui.control_panel - INFO - Results and statistics cleared
2025-06-09 19:43:14,454 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:43:38,138 - src.ui.control_panel - INFO - Batch 6 completed: 1000 records, 1000 anomalies
2025-06-09 19:43:50,913 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:44:05,442 - src.ui.control_panel - INFO - Processing already running
2025-06-09 19:44:59,354 - src.ui.control_panel - INFO - Batch 7 completed: 1000 records, 1000 anomalies
2025-06-09 19:45:58,422 - src.ui.control_panel - INFO - Batch 8 completed: 1000 records, 999 anomalies
2025-06-09 19:46:55,664 - src.ui.control_panel - INFO - Batch 9 completed: 1000 records, 1000 anomalies
2025-06-09 19:47:55,941 - src.ui.control_panel - INFO - Batch 10 completed: 1000 records, 1000 anomalies
2025-06-09 19:48:56,009 - src.ui.control_panel - INFO - Batch 11 completed: 1000 records, 999 anomalies
2025-06-09 19:49:52,617 - src.ui.control_panel - INFO - Batch 12 completed: 1000 records, 1000 anomalies
2025-06-09 19:50:49,161 - src.ui.control_panel - INFO - Batch 13 completed: 1000 records, 1000 anomalies
2025-06-09 19:51:46,083 - src.ui.control_panel - INFO - Batch 14 completed: 1000 records, 1000 anomalies
2025-06-09 19:52:43,113 - src.ui.control_panel - INFO - Batch 15 completed: 1000 records, 1000 anomalies
2025-06-09 19:53:39,338 - src.ui.control_panel - INFO - Batch 16 completed: 1000 records, 1000 anomalies
2025-06-09 19:54:35,498 - src.ui.control_panel - INFO - Batch 17 completed: 1000 records, 1000 anomalies
2025-06-09 19:55:41,276 - src.ui.control_panel - INFO - Batch 18 completed: 1000 records, 1000 anomalies
2025-06-09 19:57:00,706 - src.ui.control_panel - INFO - Batch 19 completed: 1000 records, 999 anomalies
2025-06-09 19:58:30,611 - src.ui.control_panel - INFO - Batch 20 completed: 1000 records, 1000 anomalies
2025-06-09 19:59:05,918 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 19:59:09,494 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 19:59:09,707 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 19:59:12,133 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 19:59:12,135 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 19:59:12,235 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 19:59:12,236 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 19:59:12,237 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 19:59:12,366 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 19:59:13,757 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 19:59:13,795 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 19:59:14,123 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-09 20:06:53,984 - src.ui.control_panel - INFO - Results and statistics cleared
2025-06-09 20:12:59,029 - src.core.data_loader - INFO - Loading dataset from: data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 20:13:00,283 - src.core.data_loader - INFO - Dataset loaded successfully. Shape: (415890, 48)
2025-06-09 20:13:00,283 - src.ui.control_panel - INFO - Data loader initialized successfully
2025-06-09 20:13:01,333 - src.models.anomaly_detector - INFO - Binary model loaded from: models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 20:13:01,333 - src.models.anomaly_detector - INFO - Binary scaler loaded from: models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 20:13:01,388 - src.models.anomaly_detector - INFO - Multiclass model loaded from: models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 20:13:01,388 - src.models.anomaly_detector - INFO - Multiclass scaler loaded from: models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 20:13:01,388 - src.models.anomaly_detector - INFO - All models and scalers loaded successfully
2025-06-09 20:13:01,388 - src.ui.control_panel - INFO - Models loaded successfully
2025-06-09 20:13:02,113 - httpx - INFO - HTTP Request: GET http://127.0.0.1:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-06-09 20:13:02,135 - httpx - INFO - HTTP Request: HEAD http://127.0.0.1:7860/ "HTTP/1.1 200 OK"
2025-06-09 20:13:02,900 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
