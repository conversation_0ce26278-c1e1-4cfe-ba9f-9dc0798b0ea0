#!/usr/bin/env python3
"""
System Test Script
Tests all components of the 5G Security Analytics system
"""

import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.data_loader import DataLoader
from src.models.anomaly_detector import AnomalyDetector
from src.core.batch_processor import BatchProcessor
from src.utils.metrics import MetricsCollector, calculate_detection_metrics

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_data_loader():
    """Test data loading functionality"""
    logger.info("🔍 Testing Data Loader...")
    
    data_path = "data/sample_inputs/5g_nidd_dataset.csv"
    loader = DataLoader(data_path)
    
    if loader.df is None:
        logger.error("❌ Data loader failed")
        return False
    
    info = loader.get_dataset_info()
    logger.info(f"✅ Dataset loaded: {info['total_records']:,} records")
    logger.info(f"   Columns: {len(info['columns'])}")
    logger.info(f"   Has malicious labels: {info['has_malicious_label']}")
    
    # Test batch loading
    batch = loader.get_batch(10)
    if batch is not None and len(batch) > 0:
        logger.info(f"✅ Batch loading works: {len(batch)} records")
    else:
        logger.error("❌ Batch loading failed")
        return False
    
    return True


def test_anomaly_detector():
    """Test anomaly detection models"""
    logger.info("🔍 Testing Anomaly Detector...")
    
    detector = AnomalyDetector("models/rf_models")
    
    if detector.binary_model is None or detector.multiclass_model is None:
        logger.error("❌ Models not loaded")
        return False
    
    logger.info("✅ Models loaded successfully")
    
    # Test with sample data
    sample_data = {
        'seq': 1000, 'offset': 0, 'sttl': 64, 'ackdat': 0, 'tcprtt': 0.1,
        'smeanpktsz': 100, 'shops': 80, 'dttl': 64, 'srcbytes': 1000,
        'totbytes': 2000, 'dmeanpktsz': 100, 'srcwin': 8192, 'stos': 0,
        'srctcpbase': 1000, 'dstloss': 0, 'loss': 0
    }
    
    result = detector.detect_anomaly(sample_data)
    
    if "error" in result:
        logger.error(f"❌ Detection failed: {result['error']}")
        return False
    
    logger.info(f"✅ Detection works: Anomaly={result['is_anomaly']}, Type={result['attack_type']}")
    logger.info(f"   Binary confidence: {result['binary_confidence']:.3f}")
    logger.info(f"   Processing time: {result['total_processing_time_ms']:.1f}ms")
    
    return True


def test_batch_processor():
    """Test batch processing functionality"""
    logger.info("🔍 Testing Batch Processor...")
    
    # Initialize components
    detector = AnomalyDetector("models/rf_models")
    processor = BatchProcessor(detector)
    loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
    
    if detector.binary_model is None or loader.df is None:
        logger.error("❌ Prerequisites not met for batch processor test")
        return False
    
    # Get test batch
    batch_df = loader.get_batch(20)
    batch_data = batch_df.to_dict('records')
    
    logger.info(f"Processing batch of {len(batch_data)} records...")
    
    # Test sequential processing
    results = processor.process_batch_sequential(batch_data)
    
    if len(results) != len(batch_data):
        logger.error("❌ Batch processing failed: result count mismatch")
        return False
    
    # Analyze results
    anomaly_count = sum(1 for r in results if r.get("is_anomaly", False))
    avg_time = sum(r.get("total_processing_time_ms", 0) for r in results) / len(results)
    
    logger.info(f"✅ Batch processing works:")
    logger.info(f"   Processed: {len(results)} records")
    logger.info(f"   Anomalies: {anomaly_count}")
    logger.info(f"   Avg time: {avg_time:.1f}ms per record")
    
    # Test performance stats
    stats = processor.get_stats()
    logger.info(f"   Throughput: {stats['throughput_records_per_sec']:.1f} records/sec")
    
    return True


def test_metrics():
    """Test metrics calculation"""
    logger.info("🔍 Testing Metrics...")
    
    # Create sample results
    sample_results = [
        {"is_anomaly": True, "labelmalicious": 1, "attack_type": "UDPFlood"},
        {"is_anomaly": False, "labelmalicious": 0, "attack_type": "Normal"},
        {"is_anomaly": True, "labelmalicious": 1, "attack_type": "SYNScan"},
        {"is_anomaly": False, "labelmalicious": 0, "attack_type": "Normal"},
        {"is_anomaly": True, "labelmalicious": 0, "attack_type": "HTTPFlood"},  # False positive
    ]
    
    metrics = calculate_detection_metrics(sample_results)
    
    if "error" in metrics:
        logger.error(f"❌ Metrics calculation failed: {metrics['error']}")
        return False
    
    logger.info("✅ Metrics calculation works:")
    logger.info(f"   Accuracy: {metrics['accuracy']:.3f}")
    logger.info(f"   Precision: {metrics['precision']:.3f}")
    logger.info(f"   Recall: {metrics['recall']:.3f}")
    logger.info(f"   F1 Score: {metrics['f1_score']:.3f}")
    
    return True


def run_integration_test():
    """Run a complete integration test"""
    logger.info("🔍 Running Integration Test...")
    
    # Initialize all components
    loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
    detector = AnomalyDetector("models/rf_models")
    processor = BatchProcessor(detector)
    metrics_collector = MetricsCollector()
    
    if loader.df is None or detector.binary_model is None:
        logger.error("❌ Integration test prerequisites not met")
        return False
    
    # Process multiple batches
    total_processed = 0
    total_anomalies = 0
    all_results = []
    
    for batch_num in range(5):  # Process 5 batches
        batch_df = loader.get_batch(50)
        if batch_df is None or batch_df.empty:
            break
        
        batch_data = batch_df.to_dict('records')
        results = processor.process_batch_sequential(batch_data)
        
        anomaly_count = sum(1 for r in results if r.get("is_anomaly", False))
        total_processed += len(results)
        total_anomalies += anomaly_count
        all_results.extend(results)
        
        # Update metrics
        metrics_collector.increment_counter("processed_records", len(results))
        metrics_collector.increment_counter("detected_anomalies", anomaly_count)
        
        avg_time = sum(r.get("total_processing_time_ms", 0) for r in results) / len(results)
        metrics_collector.add_metric("avg_processing_time_ms", avg_time)
        
        logger.info(f"   Batch {batch_num + 1}: {len(results)} records, {anomaly_count} anomalies")
    
    # Final statistics
    final_stats = processor.get_stats()
    metrics_summary = metrics_collector.get_summary()
    
    logger.info("✅ Integration test completed:")
    logger.info(f"   Total processed: {total_processed} records")
    logger.info(f"   Total anomalies: {total_anomalies}")
    logger.info(f"   Anomaly rate: {(total_anomalies/total_processed)*100:.1f}%")
    logger.info(f"   Throughput: {final_stats['throughput_records_per_sec']:.1f} records/sec")
    logger.info(f"   Avg processing time: {metrics_summary['metrics'].get('avg_processing_time_ms', {}).get('average', 0):.1f}ms")
    
    return True


def main():
    """Run all tests"""
    logger.info("🚀 Starting System Tests")
    
    tests = [
        ("Data Loader", test_data_loader),
        ("Anomaly Detector", test_anomaly_detector),
        ("Batch Processor", test_batch_processor),
        ("Metrics", test_metrics),
        ("Integration", run_integration_test)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} test PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} test FAILED")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} test ERROR: {e}")
            failed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"🏁 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All tests passed! System is ready.")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
