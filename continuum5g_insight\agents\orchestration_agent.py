# Continuum5G Insight - Orchestration & Workflow Agent
# Built on NVIDIA AIQ Toolkit (conceptual), coordinates all other agents.
# May explore NVIDIA Dynamo for optimization.

import os
import json
import time
import datetime 
import logging

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

# Assuming agent files are in the same directory or sys.path is handled by the caller (e.g. main.py)
from .data_streaming_agent import DataStreamer 
from .anomaly_detection_agent import AnomalyDetectionAgent, <PERSON><PERSON><PERSON>_PATH_BINARY, M<PERSON><PERSON>_PATH_MULTICLASS, M<PERSON><PERSON>_PATH_BINARY_SCALER, MODEL_PATH_MULTI_SCALER
from .threat_analysis_agent import ThreatAnalysisAgent, KB_PATH_THREAT_ACTORS
from .response_strategist_agent import ResponseStrategistAgent, KB_PATH_RESPONSE_PLAYBOOKS

class OrchestrationAgent:
    def __init__(self, dataset_base_path="../../data/sample_inputs", models_base_path="../../models/rf_models", kb_base_path="../knowledge_bases"):
        logger.info("Initializing OrchestrationAgent...")
        current_agent_dir = os.path.dirname(__file__)

        # Resolve paths relative to the current agent's directory
        # For DataStreamer, it expects the full path or path relative to where it's run from.
        # Orchestrator will construct the full path for the streamer.
        self.dataset_base_path = os.path.abspath(os.path.join(current_agent_dir, dataset_base_path))

        # Model paths for AnomalyDetectionAgent are relative to anomaly_detection_agent.py
        # AnomalyDetectionAgent handles resolving them internally.
        # We pass the placeholder constants it defines.
        self.anomaly_detector = AnomalyDetectionAgent(
            model_path_binary=MODEL_PATH_BINARY, 
            model_path_multiclass=MODEL_PATH_MULTICLASS,
            model_path_scaler_binary=MODEL_PATH_BINARY_SCALER,
            model_path_scaler_multi=MODEL_PATH_MULTI_SCALER
        )

        # KB paths for ThreatAnalysisAgent and ResponseStrategistAgent are relative to their respective files.
        # They also handle resolving them internally.
        self.threat_analyzer = ThreatAnalysisAgent(kb_path=KB_PATH_THREAT_ACTORS)
        self.response_strategist = ResponseStrategistAgent(kb_path=KB_PATH_RESPONSE_PLAYBOOKS)
        
        self.data_streamer = None # Will be initialized in process_data_stream

        logger.info("OrchestrationAgent initialized with agent instances.")
        logger.info(f"DataStreamer will look for datasets in: {self.dataset_base_path}")
        # Logging for other agents' paths is done within their own __init__

    def process_data_stream(self, dataset_filename="5g_nidd_dataset.csv.placeholder", max_records_to_process=10, stream_frequency_hz=1):
        """
        Processes a stream of data: DataStreamer -> AnomalyDetector -> ThreatAnalyzer -> ResponseStrategist.
        Logs the final response strategy.
        """
        full_dataset_path = os.path.join(self.dataset_base_path, dataset_filename)
        logger.info(f"Starting data stream processing for: {full_dataset_path}")

        if not os.path.exists(full_dataset_path):
            # Attempt to remove .placeholder if it exists and check again
            if dataset_filename.endswith(".placeholder"):
                actual_filename = dataset_filename.replace(".placeholder", "")
                full_dataset_path_actual = os.path.join(self.dataset_base_path, actual_filename)
                if os.path.exists(full_dataset_path_actual):
                    logger.info(f"Using actual dataset file: {full_dataset_path_actual}")
                    full_dataset_path = full_dataset_path_actual
                else:
                    logger.error(f"Dataset file not found: {full_dataset_path} (and actual {full_dataset_path_actual} not found either)")
                    return
            else:
                logger.error(f"Dataset file not found: {full_dataset_path}")
                return

        self.data_streamer = DataStreamer(csv_path=full_dataset_path, default_frequency_hz=stream_frequency_hz)
        if self.data_streamer.df is None or self.data_streamer.df.empty:
            logger.error(f"Failed to load data from {full_dataset_path}. Aborting stream processing.")
            return

        logger.info(f"Processing up to {max_records_to_process} records from the stream...")
        
        processed_count = 0
        for data_record in self.data_streamer.send_n_records(max_records_to_process):
            if data_record is None: # Should not happen with send_n_records if df is loaded
                logger.warning("Received None from data streamer, ending processing.")
                break
            
            logger.info(f"\n--- Record {processed_count + 1} ---")
            logger.debug(f"Raw Record from Streamer: {data_record}")

            # 1. Anomaly Detection
            detection_alert = self.anomaly_detector.detect_anomalies(data_record)
            if detection_alert.get('error'):
                logger.error(f"Anomaly detection failed: {detection_alert['error']}. Alert: {detection_alert}")
                # Optionally decide if to continue to next record or stop
                processed_count += 1
                if processed_count >= max_records_to_process:
                    break
                time.sleep(1.0 / stream_frequency_hz) # Respect frequency even on error
                continue 
            logger.info(f"Detection Alert: Binary={detection_alert.get('binary_prediction')}, Multiclass={detection_alert.get('multiclass_prediction')}")

            # 2. Threat Analysis (only if anomaly detected or for all records based on policy)
            # For this MVP, we analyze if binary_prediction is 1 (anomaly)
            if detection_alert.get("binary_prediction") == 1:
                logger.info("Anomaly detected, proceeding to Threat Analysis.")
                enriched_alert = self.threat_analyzer.analyze_threat(detection_alert)
                logger.info(f"Enriched Alert: Summary='{enriched_alert.get('analysis_summary')}', Priority={enriched_alert.get('priority_assessment')}")

                # 3. Response Strategy
                response_strategy = self.response_strategist.generate_response_strategy(enriched_alert)
                logger.info(f"Suggested Response Plan: {response_strategy.get('suggested_plan', {}).get('name', 'N/A')}")
                logger.debug(f"Full Response Strategy: {json.dumps(response_strategy, indent=2, default=str)}")
            else:
                logger.info("No anomaly detected by binary model (or model not loaded). Skipping Threat Analysis and Response Strategy for this record.")
            
            processed_count += 1
            if processed_count >= max_records_to_process:
                logger.info(f"Processed {max_records_to_process} records. Halting.")
                break
            
            # Simulate processing delay according to frequency
            time.sleep(1.0 / stream_frequency_hz)

        logger.info("Data stream processing finished.")

if __name__ == '__main__':
    # Basic logging setup for direct testing of this script
    import sys # Ensure sys is imported
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', stream=sys.stdout)
    logger.info("--- Direct Test of OrchestrationAgent (with Logging) ---")
    
    current_script_dir = os.path.dirname(os.path.abspath(__file__))
    
    test_dataset_base = "../../data/sample_inputs" 
    
    kb_threat_actors_path_abs = os.path.abspath(os.path.join(current_script_dir, KB_PATH_THREAT_ACTORS))
    kb_response_path_abs = os.path.abspath(os.path.join(current_script_dir, KB_PATH_RESPONSE_PLAYBOOKS))

    kb_threat_actors_dir = os.path.dirname(kb_threat_actors_path_abs)
    kb_response_dir = os.path.dirname(kb_response_path_abs)

    if not os.path.exists(kb_threat_actors_dir):
        os.makedirs(kb_threat_actors_dir, exist_ok=True)
        logger.info(f"Test: Created dir for threat_actors_kb: {kb_threat_actors_dir}")
    if not os.path.exists(kb_response_dir):
        os.makedirs(kb_response_dir, exist_ok=True)
        logger.info(f"Test: Created dir for response_playbooks_kb: {kb_response_dir}")

    if not os.path.exists(kb_threat_actors_path_abs) or os.path.getsize(kb_threat_actors_path_abs) == 0:
        with open(kb_threat_actors_path_abs, 'w') as f:
            json.dump({"PortScan": {"description": "Test PortScan entry"}}, f, indent=2)
        logger.info(f"Test: Created/Populated dummy threat actor KB for direct test: {kb_threat_actors_path_abs}")
        
    if not os.path.exists(kb_response_path_abs) or os.path.getsize(kb_response_path_abs) == 0:
        with open(kb_response_path_abs, 'w') as f:
            json.dump({"PortScan": {"playbook_name": "Test PortScan Playbook", "initial_actions": ["Log source IP"]}}, f, indent=2)
        logger.info(f"Test: Created/Populated dummy response playbook KB for direct test: {kb_response_path_abs}")

    orchestrator = OrchestrationAgent(dataset_base_path=test_dataset_base)
    
    if orchestrator.anomaly_detector.binary_model is None and \
       orchestrator.anomaly_detector.multiclass_model is None and \
       orchestrator.anomaly_detector.scaler is None:
        logger.error("\nNo anomaly detection models/scaler loaded. Orchestration test cannot proceed effectively.")
    else:
        orchestrator.process_data_stream(dataset_filename="5g_nidd_dataset.csv.placeholder", max_records_to_process=3)
    
    logger.info("--- Finished Direct Test of OrchestrationAgent ---")
