# Continuum5G Insight - High-Volume Processing Orchestrator
# Optimizes processing pipeline for high-volume input scenarios using NVIDIA technologies

import logging
import time
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional, Iterator
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from continuum5g_insight.core.utils import select_optimal_device, get_gpu_device_info
from continuum5g_insight.agents.rapids_preprocessing_agent import RAPIDSPreprocessingAgent
from continuum5g_insight.agents.anomaly_detection_agent import AnomalyDetectionAgent

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

class HighVolumeProcessingOrchestrator:
    """
    Orchestrates high-volume data processing using NVIDIA GPU acceleration.
    Implements adaptive processing strategies based on available hardware.
    """
    
    def __init__(self, binary_model_path, multiclass_model_path, 
                 binary_scaler_path, multi_scaler_path, 
                 max_workers: Optional[int] = None):
        logger.info("Initializing High-Volume Processing Orchestrator...")
        
        # Device and capability detection
        self.device_type, self.device_info = select_optimal_device()
        self.gpu_available = self.device_info.get('gpu_info', {}).get('gpu_available', False)
        self.cuml_available = self.device_info.get('cuml_available', False)
        self.cudf_available = self.device_info.get('cudf_available', False)
        
        # Initialize processing agents
        self.preprocessing_agent = RAPIDSPreprocessingAgent()
        self.detection_agent = AnomalyDetectionAgent(
            binary_model_path, multiclass_model_path, 
            binary_scaler_path, multi_scaler_path
        )
        
        # Determine optimal processing strategy
        self.processing_strategy = self._determine_processing_strategy()
        self.max_workers = max_workers or self._calculate_optimal_workers()
        
        # Performance tracking
        self.performance_metrics = {
            "total_records_processed": 0,
            "total_processing_time": 0.0,
            "peak_throughput_rps": 0.0,
            "average_throughput_rps": 0.0,
            "gpu_utilization_sessions": 0,
            "cpu_fallback_sessions": 0,
            "processing_strategy": self.processing_strategy,
            "batch_sizes": [],
            "processing_times": []
        }
        
        logger.info(f"Orchestrator initialized with strategy: {self.processing_strategy}")
        logger.info(f"Max workers: {self.max_workers}, GPU available: {self.gpu_available}")
    
    def _determine_processing_strategy(self) -> str:
        """Determine optimal processing strategy based on available hardware."""
        if self.gpu_available and self.cudf_available and self.cuml_available:
            return "gpu_accelerated"
        elif self.gpu_available and self.cudf_available:
            return "gpu_preprocessing_cpu_inference"
        elif self.gpu_available:
            return "gpu_aware_cpu_processing"
        else:
            return "cpu_optimized"
    
    def _calculate_optimal_workers(self) -> int:
        """Calculate optimal number of worker processes/threads."""
        cpu_count = mp.cpu_count()
        
        if self.gpu_available:
            # GPU can handle more parallel streams
            gpu_info = get_gpu_device_info()
            gpu_memory_gb = gpu_info.get('total_memory_mb', 0) / 1024
            
            if gpu_memory_gb >= 8:
                return min(cpu_count * 2, 16)  # High-memory GPU
            elif gpu_memory_gb >= 4:
                return min(cpu_count, 8)       # Medium-memory GPU
            else:
                return min(cpu_count // 2, 4)  # Low-memory GPU
        else:
            return min(cpu_count, 4)  # CPU-only processing
    
    def process_high_volume_stream(self, data_stream: Iterator, 
                                 batch_size: int = 10000,
                                 adaptive_batching: bool = True) -> Iterator[List[Dict]]:
        """
        Process high-volume data stream with adaptive optimization.
        
        Args:
            data_stream: Iterator yielding data records
            batch_size: Initial batch size for processing
            adaptive_batching: Whether to adaptively adjust batch size
            
        Yields:
            Processed alert batches
        """
        logger.info(f"Starting high-volume stream processing with strategy: {self.processing_strategy}")
        
        current_batch_size = batch_size
        processing_start_time = time.time()
        
        for batch_data in self._batch_data_stream(data_stream, current_batch_size):
            batch_start_time = time.time()
            
            try:
                # Process batch using optimal strategy
                if self.processing_strategy == "gpu_accelerated":
                    alerts = self._process_batch_gpu_accelerated(batch_data)
                elif self.processing_strategy == "gpu_preprocessing_cpu_inference":
                    alerts = self._process_batch_hybrid(batch_data)
                else:
                    alerts = self._process_batch_cpu_optimized(batch_data)
                
                # Calculate performance metrics
                batch_time = time.time() - batch_start_time
                batch_throughput = len(batch_data) / batch_time if batch_time > 0 else 0
                
                # Update metrics
                self._update_performance_metrics(len(batch_data), batch_time, batch_throughput)
                
                # Adaptive batching optimization
                if adaptive_batching:
                    current_batch_size = self._adapt_batch_size(current_batch_size, batch_throughput, batch_time)
                
                logger.info(f"Processed batch of {len(batch_data)} records in {batch_time:.3f}s "
                           f"({batch_throughput:.1f} rps)")
                
                yield alerts
                
            except Exception as e:
                logger.error(f"Error processing batch: {e}", exc_info=True)
                # Yield empty alerts to maintain stream continuity
                yield []
        
        total_time = time.time() - processing_start_time
        logger.info(f"High-volume processing completed. Total time: {total_time:.3f}s, "
                   f"Total records: {self.performance_metrics['total_records_processed']}")
    
    def _batch_data_stream(self, data_stream: Iterator, batch_size: int) -> Iterator[List]:
        """Convert data stream into batches for processing."""
        batch = []
        for record in data_stream:
            batch.append(record)
            if len(batch) >= batch_size:
                yield batch
                batch = []
        
        # Yield remaining records
        if batch:
            yield batch
    
    def _process_batch_gpu_accelerated(self, batch_data: List[Dict]) -> List[Dict]:
        """Process batch using full GPU acceleration."""
        logger.debug(f"Processing batch with GPU acceleration: {len(batch_data)} records")
        
        try:
            # Stage 1: GPU-accelerated preprocessing
            processed_data = self.preprocessing_agent.ingest_batch_data(
                batch_data, source_type="records"
            )
            
            if processed_data is None:
                logger.warning("GPU preprocessing failed, falling back to CPU")
                return self._process_batch_cpu_optimized(batch_data)
            
            # Stage 2: Parallel GPU inference
            alerts = []
            
            # Convert to pandas for compatibility with current inference
            if hasattr(processed_data, 'to_pandas'):
                processed_data = processed_data.to_pandas()
            
            # Process records in parallel
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                for _, record in processed_data.iterrows():
                    record_dict = record.to_dict()
                    future = executor.submit(self.detection_agent.detect_anomalies, record_dict)
                    futures.append(future)
                
                for future in futures:
                    try:
                        alert = future.result(timeout=10)  # 10 second timeout per record
                        alerts.append(alert)
                    except Exception as e:
                        logger.error(f"Error in parallel inference: {e}")
                        alerts.append({"error": str(e), "timestamp": datetime.now().isoformat()})
            
            self.performance_metrics["gpu_utilization_sessions"] += 1
            return alerts
            
        except Exception as e:
            logger.error(f"GPU accelerated processing failed: {e}")
            self.performance_metrics["cpu_fallback_sessions"] += 1
            return self._process_batch_cpu_optimized(batch_data)
    
    def _process_batch_hybrid(self, batch_data: List[Dict]) -> List[Dict]:
        """Process batch using GPU preprocessing and CPU inference."""
        logger.debug(f"Processing batch with hybrid GPU/CPU: {len(batch_data)} records")
        
        try:
            # GPU preprocessing
            processed_data = self.preprocessing_agent.ingest_batch_data(
                batch_data, source_type="records"
            )
            
            if processed_data is not None:
                # Convert to pandas for CPU inference
                if hasattr(processed_data, 'to_pandas'):
                    processed_data = processed_data.to_pandas()
                
                # CPU inference in parallel
                alerts = []
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = []
                    for _, record in processed_data.iterrows():
                        record_dict = record.to_dict()
                        future = executor.submit(self.detection_agent.detect_anomalies, record_dict)
                        futures.append(future)
                    
                    for future in futures:
                        try:
                            alert = future.result(timeout=10)
                            alerts.append(alert)
                        except Exception as e:
                            logger.error(f"Error in hybrid inference: {e}")
                            alerts.append({"error": str(e), "timestamp": datetime.now().isoformat()})
                
                return alerts
            else:
                # Fallback to CPU processing
                return self._process_batch_cpu_optimized(batch_data)
                
        except Exception as e:
            logger.error(f"Hybrid processing failed: {e}")
            return self._process_batch_cpu_optimized(batch_data)
    
    def _process_batch_cpu_optimized(self, batch_data: List[Dict]) -> List[Dict]:
        """Process batch using optimized CPU processing."""
        logger.debug(f"Processing batch with CPU optimization: {len(batch_data)} records")
        
        alerts = []
        
        try:
            # Use sequential processing to maintain model consistency
            # This ensures the same threshold and model behavior as single-record processing
            for record in batch_data:
                try:
                    alert = self.detection_agent.detect_anomalies(record)
                    # Wrap the result in the expected format for batch processing
                    if 'detection_result' not in alert:
                        wrapped_alert = {
                            'detection_result': alert,
                            'performance_metrics': {
                                'processing_strategy': 'cpu_optimized_sequential'
                            }
                        }
                        alerts.append(wrapped_alert)
                    else:
                        alerts.append(alert)
                except Exception as e:
                    logger.error(f"Sequential processing error: {e}")
                    alerts.append({
                        "detection_result": {"error": str(e), "timestamp": datetime.now().isoformat()},
                        "performance_metrics": {}
                    })
            
            return alerts
            
        except Exception as e:
            logger.error(f"CPU processing failed: {e}")
            # Fallback with error results
            return [{
                "detection_result": {"error": str(e), "timestamp": datetime.now().isoformat()},
                "performance_metrics": {}
            } for _ in batch_data]
    
    def _single_record_detection(self, record: Dict) -> Dict:
        """Helper method for single record detection (for multiprocessing)."""
        try:
            # Use the existing detection agent to maintain consistency
            # Instead of creating a new agent, use sequential processing with the existing agent
            return self.detection_agent.detect_anomalies(record)
        except Exception as e:
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def _adapt_batch_size(self, current_batch_size: int, throughput: float, processing_time: float) -> int:
        """Adaptively adjust batch size based on performance."""
        target_processing_time = 5.0  # Target 5 seconds per batch
        
        if processing_time < target_processing_time * 0.5:
            # Processing too fast, increase batch size
            new_batch_size = min(current_batch_size * 1.5, 50000)
        elif processing_time > target_processing_time * 1.5:
            # Processing too slow, decrease batch size
            new_batch_size = max(current_batch_size * 0.7, 1000)
        else:
            # Optimal range, no change
            new_batch_size = current_batch_size
        
        if new_batch_size != current_batch_size:
            logger.info(f"Adapting batch size: {current_batch_size} -> {int(new_batch_size)}")
        
        return int(new_batch_size)
    
    def _update_performance_metrics(self, batch_size: int, processing_time: float, throughput: float):
        """Update performance tracking metrics."""
        self.performance_metrics["total_records_processed"] += batch_size
        self.performance_metrics["total_processing_time"] += processing_time
        self.performance_metrics["batch_sizes"].append(batch_size)
        self.performance_metrics["processing_times"].append(processing_time)
        
        if throughput > self.performance_metrics["peak_throughput_rps"]:
            self.performance_metrics["peak_throughput_rps"] = throughput
        
        # Calculate average throughput
        total_records = self.performance_metrics["total_records_processed"]
        total_time = self.performance_metrics["total_processing_time"]
        if total_time > 0:
            self.performance_metrics["average_throughput_rps"] = total_records / total_time
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        report = self.performance_metrics.copy()
        
        # Add derived metrics
        if self.performance_metrics["batch_sizes"]:
            report["average_batch_size"] = sum(self.performance_metrics["batch_sizes"]) / len(self.performance_metrics["batch_sizes"])
            report["average_batch_time"] = sum(self.performance_metrics["processing_times"]) / len(self.performance_metrics["processing_times"])
        
        # Add hardware utilization info
        report["hardware_info"] = {
            "device_type": self.device_type,
            "gpu_available": self.gpu_available,
            "cuml_available": self.cuml_available,
            "cudf_available": self.cudf_available,
            "max_workers": self.max_workers
        }
        
        # Add agent performance stats
        if hasattr(self.detection_agent, 'get_performance_stats'):
            report["detection_agent_stats"] = self.detection_agent.get_performance_stats()
        
        if hasattr(self.preprocessing_agent, 'get_performance_metrics'):
            report["preprocessing_agent_stats"] = self.preprocessing_agent.get_performance_metrics()
        
        return report
    
    def process_batch(self, batch_data: List[Dict]) -> List[Dict]:
        """
        Process a batch of data records using the optimal strategy.
        
        Args:
            batch_data: List of data records to process
            
        Returns:
            List of detection results with performance metrics
        """
        start_time = time.time()
        
        try:
            if self.processing_strategy == "gpu_accelerated":
                results = self._process_batch_gpu_accelerated(batch_data)
            elif self.processing_strategy == "gpu_preprocessing_cpu_inference":
                results = self._process_batch_hybrid(batch_data)
            else:
                results = self._process_batch_cpu_optimized(batch_data)
            
            processing_time = time.time() - start_time
            throughput = len(batch_data) / processing_time if processing_time > 0 else 0
            
            # Update performance metrics
            self._update_performance_metrics(len(batch_data), processing_time, throughput)
            
            # Add performance metadata to each result
            for result in results:
                if 'performance_metrics' not in result:
                    result['performance_metrics'] = {}
                result['performance_metrics'].update({
                    'batch_processing_time': processing_time,
                    'batch_throughput': throughput,
                    'processing_strategy': self.processing_strategy
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            # Return empty results with error info
            return [{'detection_result': {}, 'error': str(e), 'performance_metrics': {}} for _ in batch_data]


if __name__ == '__main__':
    # Test the High-Volume Processing Orchestrator
    import json
    import logging
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    print("=== Testing High-Volume Processing Orchestrator ===")
    
    # Model paths (adjust as needed)
    binary_model = "../../models/rf_models/model_binary/rf_model_binary.joblib"
    multiclass_model = "../../models/rf_models/model_multiclass/rf_model_multiclass.joblib"
    binary_scaler = "../../models/rf_models/model_binary/standard_scaler.joblib"
    multi_scaler = "../../models/rf_models/model_multiclass/standard_scaler.joblib"
    
    # Initialize orchestrator
    orchestrator = HighVolumeProcessingOrchestrator(
        binary_model, multiclass_model, binary_scaler, multi_scaler
    )
    
    # Create test data stream
    def generate_test_stream(num_records=50000):
        for i in range(num_records):
            yield {
                'seq': i,
                'offset': i * 1.5,
                'sttl': 64 + (i % 10),
                'ackdat': i * 2,
                'tcprtt': 50 + (i % 100),
                'smeanpktsz': 1000 + (i % 500),
                'shops': 80 + (i % 20),
                'dttl': 60 + (i % 15),
                'srcbytes': 1000 * (i % 10 + 1),
                'totbytes': 2000 * (i % 10 + 1),
                'dmeanpktsz': 900 + (i % 400),
                'srcwin': 65535,
                'stos': 0,
                'srctcpbase': 1000 + i,
                'dstloss': i % 5,
                'loss': i % 3
            }
    
    print("Starting high-volume processing test...")
    start_time = time.time()
    
    total_alerts = 0
    anomaly_count = 0
    
    # Process the stream
    for alert_batch in orchestrator.process_high_volume_stream(
        generate_test_stream(10000), 
        batch_size=2000,
        adaptive_batching=True
    ):
        total_alerts += len(alert_batch)
        anomaly_count += sum(1 for alert in alert_batch if alert.get('binary_prediction') == 1)
    
    total_time = time.time() - start_time
    
    print(f"\n=== High-Volume Processing Results ===")
    print(f"Total processing time: {total_time:.3f}s")
    print(f"Total alerts generated: {total_alerts}")
    print(f"Anomalies detected: {anomaly_count}")
    print(f"Overall throughput: {total_alerts/total_time:.1f} records/sec")
    
    # Get performance report
    performance_report = orchestrator.get_performance_report()
    print(f"\n=== Performance Report ===")
    print(json.dumps(performance_report, indent=2, default=str))
    
    print("=== End High-Volume Processing Test ===")
