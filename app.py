#!/usr/bin/env python3
"""
5G Security Analytics Control Panel
Gradio-based control interface
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.control_panel import create_control_panel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('security_analytics.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description="5G Security Analytics Control Panel")
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind the server to"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=7860,
        help="Port to bind the server to"
    )
    parser.add_argument(
        "--share",
        action="store_true",
        help="Create a public link"
    )

    args = parser.parse_args()

    logger.info("🎛️ Starting 5G Security Analytics Control Panel")
    logger.info(f"🌐 Server: {args.host}:{args.port}")
    logger.info("📊 For real-time monitoring, run: streamlit run src/ui/dashboard.py")

    try:
        # Create Gradio control panel
        demo = create_control_panel()

        # Launch the control panel
        demo.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            show_error=True,
            quiet=False
        )

    except KeyboardInterrupt:
        logger.info("Control panel stopped by user")
    except Exception as e:
        logger.error(f"Error starting control panel: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
