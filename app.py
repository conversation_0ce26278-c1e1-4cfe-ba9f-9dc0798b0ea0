#!/usr/bin/env python3
"""
5G Security Analytics Dashboard
Main application entry point
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ui.dashboard import SecurityDashboard, create_dashboard

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('security_analytics.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description="5G Security Analytics Dashboard")
    parser.add_argument(
        "--data-path", 
        default="data/sample_inputs/5g_nidd_dataset.csv",
        help="Path to the dataset CSV file"
    )
    parser.add_argument(
        "--models-path",
        default="models/rf_models", 
        help="Path to the models directory"
    )
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind the server to"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=7860,
        help="Port to bind the server to"
    )
    parser.add_argument(
        "--share",
        action="store_true",
        help="Create a public link"
    )
    
    args = parser.parse_args()
    
    # Validate paths
    if not Path(args.data_path).exists():
        logger.error(f"Dataset not found: {args.data_path}")
        sys.exit(1)
    
    if not Path(args.models_path).exists():
        logger.error(f"Models directory not found: {args.models_path}")
        sys.exit(1)
    
    logger.info("🚀 Starting 5G Security Analytics Dashboard")
    logger.info(f"📊 Dataset: {args.data_path}")
    logger.info(f"🤖 Models: {args.models_path}")
    logger.info(f"🌐 Server: {args.host}:{args.port}")
    
    try:
        # Initialize dashboard
        dashboard = SecurityDashboard(
            data_path=args.data_path,
            models_path=args.models_path
        )
        
        # Create Gradio interface
        demo = create_dashboard(dashboard)
        
        # Launch the dashboard
        demo.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            show_error=True,
            quiet=False
        )
        
    except KeyboardInterrupt:
        logger.info("Dashboard stopped by user")
    except Exception as e:
        logger.error(f"Error starting dashboard: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
