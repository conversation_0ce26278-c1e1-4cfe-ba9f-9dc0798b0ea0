# Continuum5G Insight - RAPIDS Data Preprocessing Agent
# Implements GPU-accelerated data processing using RAPIDS cuDF for high-volume input optimization

import pandas as pd
import numpy as np
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from continuum5g_insight.core.utils import is_cudf_available, select_optimal_device, get_gpu_device_info

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

class RAPIDSPreprocessingAgent:
    """
    Data Ingestion & RAPIDS Preprocessing Agent for high-speed data processing.
    Leverages RAPIDS cuDF for GPU-accelerated data manipulation when available.
    """
    
    def __init__(self):
        logger.info("Initializing RAPIDS Preprocessing Agent...")
        
        # Determine optimal processing device
        self.device_type, self.device_info = select_optimal_device()
        self.cudf_available = self.device_info.get('cudf_available', False) if self.device_type == 'gpu' else False
        
        # Initialize DataFrame libraries based on device availability
        if self.cudf_available:
            try:
                import cudf
                self.df_lib = cudf
                self.processing_mode = "gpu_cudf"
                logger.info("RAPIDS cuDF initialized for GPU-accelerated processing")
            except ImportError:
                logger.warning("cuDF import failed, falling back to pandas")
                self.df_lib = pd
                self.processing_mode = "cpu_pandas"
        else:
            self.df_lib = pd
            self.processing_mode = "cpu_pandas"
            logger.info("Using pandas for CPU-based processing")
        
        # Performance metrics
        self.processing_stats = {
            "total_records_processed": 0,
            "total_processing_time": 0.0,
            "average_throughput_rps": 0.0,
            "gpu_memory_usage_mb": 0,
            "mode": self.processing_mode
        }
        
        logger.info(f"RAPIDS Preprocessing Agent initialized in {self.processing_mode} mode")
    
    def ingest_batch_data(self, data: Union[List[Dict], pd.DataFrame, str], 
                         source_type: str = "records") -> Optional[Any]:
        """
        Ingest and preprocess batch data using GPU acceleration when available.
        
        Args:
            data: Input data (list of records, DataFrame, or file path)
            source_type: Type of data source ("records", "dataframe", "csv", "json")
            
        Returns:
            Processed DataFrame (cuDF or pandas based on availability)
        """
        start_time = time.time()
        
        try:
            # Convert input data to appropriate DataFrame format
            if source_type == "records" and isinstance(data, list):
                df = self.df_lib.DataFrame(data)
            elif source_type == "dataframe" and isinstance(data, pd.DataFrame):
                if self.cudf_available:
                    df = self.df_lib.from_pandas(data)
                else:
                    df = data
            elif source_type == "csv" and isinstance(data, str):
                df = self.df_lib.read_csv(data)
            elif source_type == "json" and isinstance(data, str):
                df = self.df_lib.read_json(data)
            else:
                logger.error(f"Unsupported data type/source combination: {type(data)}/{source_type}")
                return None
            
            logger.info(f"Ingested {len(df)} records using {self.processing_mode}")
            
            # Apply high-speed preprocessing
            processed_df = self._apply_preprocessing_pipeline(df)
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.processing_stats["total_records_processed"] += len(df)
            self.processing_stats["total_processing_time"] += processing_time
            self.processing_stats["average_throughput_rps"] = (
                self.processing_stats["total_records_processed"] / 
                self.processing_stats["total_processing_time"]
            )
            
            # Update GPU memory usage if available
            if self.cudf_available:
                try:
                    gpu_info = get_gpu_device_info()
                    if gpu_info["gpu_available"] and gpu_info["devices"]:
                        self.processing_stats["gpu_memory_usage_mb"] = gpu_info["devices"][0]["used_memory_mb"]
                except:
                    pass
            
            logger.info(f"Batch preprocessing completed in {processing_time:.3f}s, "
                       f"throughput: {len(df)/processing_time:.1f} records/sec")
            
            return processed_df
            
        except Exception as e:
            logger.error(f"Error in batch data ingestion: {e}", exc_info=True)
            return None
    
    def _apply_preprocessing_pipeline(self, df: Any) -> Any:
        """
        Apply GPU-accelerated preprocessing pipeline.
        
        Args:
            df: Input DataFrame (cuDF or pandas)
            
        Returns:
            Processed DataFrame
        """
        try:
            # Stage 1: Data Cleaning and Normalization
            df_clean = self._clean_and_normalize(df)
            
            # Stage 2: Feature Engineering
            df_features = self._engineer_features(df_clean)
            
            # Stage 3: Data Filtering (remove irrelevant data)
            df_filtered = self._apply_intelligent_filtering(df_features)
            
            logger.debug(f"Preprocessing pipeline: {len(df)} -> {len(df_filtered)} records")
            return df_filtered
            
        except Exception as e:
            logger.error(f"Error in preprocessing pipeline: {e}", exc_info=True)
            return df
    
    def _clean_and_normalize(self, df: Any) -> Any:
        """GPU-accelerated data cleaning and normalization."""
        try:
            # Remove duplicates (GPU-accelerated with cuDF)
            df_clean = df.drop_duplicates()
            
            # Handle missing values
            if self.cudf_available:
                # Use cuDF's GPU-accelerated fillna
                numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
                df_clean[numeric_columns] = df_clean[numeric_columns].fillna(0)
            else:
                # Standard pandas operations
                df_clean = df_clean.fillna(0)
            
            # Normalize string columns to lowercase
            string_columns = df_clean.select_dtypes(include=['object']).columns
            for col in string_columns:
                if hasattr(df_clean[col], 'str'):
                    df_clean[col] = df_clean[col].str.lower()
            
            return df_clean
            
        except Exception as e:
            logger.error(f"Error in data cleaning: {e}")
            return df
    
    def _engineer_features(self, df: Any) -> Any:
        """GPU-accelerated feature engineering."""
        try:
            # Create timestamp features if timestamp column exists
            if 'timestamp' in df.columns:
                if self.cudf_available:
                    # GPU-accelerated datetime operations
                    df['hour'] = df['timestamp'].dt.hour
                    df['day_of_week'] = df['timestamp'].dt.dayofweek
                else:
                    # Convert to datetime and extract features
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df['hour'] = df['timestamp'].dt.hour
                    df['day_of_week'] = df['timestamp'].dt.dayofweek
            
            # Create network flow features if relevant columns exist
            if all(col in df.columns for col in ['srcbytes', 'totbytes']):
                df['bytes_ratio'] = df['srcbytes'] / (df['totbytes'] + 1e-6)  # Avoid division by zero
            
            # Create statistical features for packet sizes
            if all(col in df.columns for col in ['smeanpktsz', 'dmeanpktsz']):
                df['pkt_size_diff'] = df['smeanpktsz'] - df['dmeanpktsz']
                df['pkt_size_ratio'] = df['smeanpktsz'] / (df['dmeanpktsz'] + 1e-6)
            
            # Create connection duration features
            if all(col in df.columns for col in ['sttl', 'dttl']):
                df['ttl_diff'] = df['sttl'] - df['dttl']
            
            return df
            
        except Exception as e:
            logger.error(f"Error in feature engineering: {e}")
            return df
    
    def _apply_intelligent_filtering(self, df: Any) -> Any:
        """Apply intelligent filtering to remove irrelevant data."""
        try:
            initial_count = len(df)
            
            # Filter out records with invalid or suspicious values
            if 'totbytes' in df.columns:
                df = df[df['totbytes'] > 0]  # Remove zero-byte flows
            
            if 'tcprtt' in df.columns:
                # Remove records with impossible RTT values (> 10 seconds)
                df = df[df['tcprtt'] <= 10000]
            
            # Additional domain-specific filtering can be added here
            
            filtered_count = len(df)
            if initial_count > filtered_count:
                logger.debug(f"Filtering removed {initial_count - filtered_count} irrelevant records")
            
            return df
            
        except Exception as e:
            logger.error(f"Error in data filtering: {e}")
            return df
    
    def process_streaming_data(self, data_stream, batch_size: int = 1000) -> Any:
        """
        Process streaming data in batches for optimal GPU utilization.
        
        Args:
            data_stream: Iterator or generator yielding data records
            batch_size: Number of records to process in each batch
            
        Yields:
            Processed data batches
        """
        batch = []
        
        for record in data_stream:
            batch.append(record)
            
            if len(batch) >= batch_size:
                # Process the batch
                processed_batch = self.ingest_batch_data(batch, source_type="records")
                if processed_batch is not None:
                    yield processed_batch
                
                # Reset batch
                batch = []
        
        # Process remaining records
        if batch:
            processed_batch = self.ingest_batch_data(batch, source_type="records")
            if processed_batch is not None:
                yield processed_batch
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics."""
        metrics = self.processing_stats.copy()
        
        # Add current GPU status if available
        if self.cudf_available:
            try:
                gpu_info = get_gpu_device_info()
                metrics["current_gpu_info"] = gpu_info
            except:
                pass
        
        return metrics
    
    def to_pandas(self, df: Any) -> pd.DataFrame:
        """Convert cuDF DataFrame to pandas for compatibility."""
        if self.cudf_available and hasattr(df, 'to_pandas'):
            return df.to_pandas()
        return df
    
    def optimize_for_inference(self, df: Any, target_columns: List[str]) -> Any:
        """
        Optimize DataFrame for ML inference by selecting relevant columns and ensuring proper data types.
        
        Args:
            df: Input DataFrame
            target_columns: List of required columns for inference
            
        Returns:
            Optimized DataFrame
        """
        try:
            # Select only required columns
            if all(col in df.columns for col in target_columns):
                df_optimized = df[target_columns].copy()
            else:
                missing_cols = set(target_columns) - set(df.columns)
                logger.warning(f"Missing columns for inference: {missing_cols}")
                available_cols = [col for col in target_columns if col in df.columns]
                df_optimized = df[available_cols].copy()
            
            # Ensure numeric data types for ML inference
            numeric_columns = df_optimized.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                df_optimized[col] = df_optimized[col].astype(np.float32)  # Use float32 for GPU efficiency
            
            logger.debug(f"Optimized DataFrame for inference: {df_optimized.shape}")
            return df_optimized
            
        except Exception as e:
            logger.error(f"Error optimizing DataFrame for inference: {e}")
            return df


if __name__ == '__main__':
    # Test the RAPIDS Preprocessing Agent
    import json
    import logging
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    print("=== Testing RAPIDS Preprocessing Agent ===")
    
    # Initialize agent
    agent = RAPIDSPreprocessingAgent()
    
    # Create test data
    test_data = []
    for i in range(10000):
        record = {
            'seq': i,
            'offset': i * 1.5,
            'sttl': 64 + (i % 10),
            'ackdat': i * 2,
            'tcprtt': 50 + (i % 100),
            'smeanpktsz': 1000 + (i % 500),
            'shops': 80 + (i % 20),
            'dttl': 60 + (i % 15),
            'srcbytes': 1000 * (i % 10 + 1),
            'totbytes': 2000 * (i % 10 + 1),
            'dmeanpktsz': 900 + (i % 400),
            'srcwin': 65535,
            'stos': 0,
            'timestamp': datetime.now().isoformat()
        }
        test_data.append(record)
    
    print(f"Created {len(test_data)} test records")
    
    # Test batch processing
    start_time = time.time()
    processed_data = agent.ingest_batch_data(test_data, source_type="records")
    processing_time = time.time() - start_time
    
    if processed_data is not None:
        print(f"Processed {len(processed_data)} records in {processing_time:.3f}s")
        print(f"Throughput: {len(processed_data)/processing_time:.1f} records/sec")
        print(f"Processing mode: {agent.processing_mode}")
        
        # Show performance metrics
        metrics = agent.get_performance_metrics()
        print(f"Performance metrics: {json.dumps(metrics, indent=2, default=str)}")
        
        # Test optimization for inference
        feature_columns = ['seq', 'offset', 'sttl', 'ackdat', 'tcprtt', 'smeanpktsz', 'shops', 'dttl', 'srcbytes', 'totbytes']
        optimized_data = agent.optimize_for_inference(processed_data, feature_columns)
        print(f"Optimized data shape: {optimized_data.shape}")
        
        # Convert to pandas if needed for compatibility
        if agent.cudf_available:
            pandas_data = agent.to_pandas(optimized_data)
            print(f"Converted to pandas: {type(pandas_data)}")
    
    print("=== End RAPIDS Preprocessing Test ===")
