"""
Anomaly Detection Module
Handles RandomForest-based binary and multiclass classification
"""

import joblib
import pandas as pd
import numpy as np
import os
import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class AnomalyDetector:
    """
    RandomForest-based anomaly detection for 5G network traffic
    """
    
    # Feature columns for binary classification
    BINARY_FEATURES = [
        'seq', 'offset', 'sttl', 'ackdat', 'tcprtt', 'smeanpktsz', 
        'shops', 'dttl', 'srcbytes', 'totbytes', 'dmeanpktsz', 'srcwin', 'stos'
    ]
    
    # Feature columns for multiclass classification
    MULTICLASS_FEATURES = [
        'ackdat', 'shops', 'seq', 'tcprtt', 'dmeanpktsz', 'offset', 'sttl',
        'srctcpbase', 'smeanpktsz', 'dstloss', 'loss', 'dttl', 'srcbytes', 'totbytes'
    ]
    
    # Class labels mapping
    CLASS_LABELS = {
        0: 'Benign', 1: 'UDPFlood', 2: 'HTTPFlood', 3: 'SlowrateDoS',
        4: 'TCPConnectScan', 5: 'SYNScan', 6: 'UDPScan',
        7: 'SYNFlood', 8: 'ICMPFlood'
    }
    
    def __init__(self, models_path: str = "models/rf_models"):
        """
        Initialize the anomaly detector
        
        Args:
            models_path: Path to the directory containing model files
        """
        self.models_path = Path(models_path)
        self.binary_model = None
        self.multiclass_model = None
        self.binary_scaler = None
        self.multiclass_scaler = None
        
        # Performance tracking
        self.stats = {
            "total_predictions": 0,
            "binary_predictions": 0,
            "multiclass_predictions": 0,
            "total_time": 0.0,
            "avg_time_per_prediction": 0.0
        }
        
        self.load_models()
    
    def load_models(self) -> bool:
        """
        Load all required models and scalers
        
        Returns:
            bool: True if all models loaded successfully
        """
        try:
            # Load binary classification model and scaler
            binary_model_path = self.models_path / "model_binary" / "rf_model_binary.joblib"
            binary_scaler_path = self.models_path / "model_binary" / "standard_scaler.joblib"
            
            if binary_model_path.exists():
                self.binary_model = joblib.load(binary_model_path)
                logger.info(f"Binary model loaded from: {binary_model_path}")
            else:
                logger.error(f"Binary model not found: {binary_model_path}")
                return False
            
            if binary_scaler_path.exists():
                self.binary_scaler = joblib.load(binary_scaler_path)
                logger.info(f"Binary scaler loaded from: {binary_scaler_path}")
            else:
                logger.error(f"Binary scaler not found: {binary_scaler_path}")
                return False
            
            # Load multiclass classification model and scaler
            multi_model_path = self.models_path / "model_multiclass" / "rf_model_multiclass.joblib"
            multi_scaler_path = self.models_path / "model_multiclass" / "standard_scaler.joblib"
            
            if multi_model_path.exists():
                self.multiclass_model = joblib.load(multi_model_path)
                logger.info(f"Multiclass model loaded from: {multi_model_path}")
            else:
                logger.error(f"Multiclass model not found: {multi_model_path}")
                return False
            
            if multi_scaler_path.exists():
                self.multiclass_scaler = joblib.load(multi_scaler_path)
                logger.info(f"Multiclass scaler loaded from: {multi_scaler_path}")
            else:
                logger.error(f"Multiclass scaler not found: {multi_scaler_path}")
                return False
            
            logger.info("All models and scalers loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False
    
    def preprocess_data(self, data: Dict[str, Any], features: List[str], scaler) -> Optional[np.ndarray]:
        """
        Preprocess input data for model prediction
        
        Args:
            data: Input data dictionary
            features: List of feature column names
            scaler: Fitted scaler object
            
        Returns:
            Preprocessed data array or None if preprocessing fails
        """
        try:
            # Convert to DataFrame and select features
            df = pd.DataFrame([data])
            df_features = df[features]
            
            # Apply scaling
            scaled_data = scaler.transform(df_features)
            return scaled_data
            
        except Exception as e:
            logger.error(f"Error preprocessing data: {e}")
            return None
    
    def predict_binary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform binary classification (anomaly/normal)
        
        Args:
            data: Input data dictionary
            
        Returns:
            Dictionary containing prediction results
        """
        if self.binary_model is None or self.binary_scaler is None:
            return {"error": "Binary model or scaler not loaded"}
        
        start_time = time.time()
        
        # Preprocess data
        processed_data = self.preprocess_data(data, self.BINARY_FEATURES, self.binary_scaler)
        if processed_data is None:
            return {"error": "Data preprocessing failed"}
        
        try:
            # Get prediction and probabilities
            probabilities = self.binary_model.predict_proba(processed_data)[0]
            prediction = 1 if probabilities[1] >= 0.3 else 0  # Threshold at 0.3
            
            processing_time = time.time() - start_time
            self.stats["binary_predictions"] += 1
            self.stats["total_time"] += processing_time
            
            return {
                "prediction": prediction,
                "probabilities": probabilities.tolist(),
                "confidence": float(probabilities[1]),
                "processing_time_ms": processing_time * 1000,
                "is_anomaly": prediction == 1
            }
            
        except Exception as e:
            logger.error(f"Error in binary prediction: {e}")
            return {"error": f"Binary prediction failed: {e}"}
    
    def predict_multiclass(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform multiclass classification (attack type)
        
        Args:
            data: Input data dictionary
            
        Returns:
            Dictionary containing prediction results
        """
        if self.multiclass_model is None or self.multiclass_scaler is None:
            return {"error": "Multiclass model or scaler not loaded"}
        
        start_time = time.time()
        
        # Preprocess data
        processed_data = self.preprocess_data(data, self.MULTICLASS_FEATURES, self.multiclass_scaler)
        if processed_data is None:
            return {"error": "Data preprocessing failed"}
        
        try:
            # Get prediction and probabilities
            prediction_idx = int(self.multiclass_model.predict(processed_data)[0])
            probabilities = self.multiclass_model.predict_proba(processed_data)[0]
            
            processing_time = time.time() - start_time
            self.stats["multiclass_predictions"] += 1
            self.stats["total_time"] += processing_time
            
            return {
                "prediction_index": prediction_idx,
                "prediction_label": self.CLASS_LABELS.get(prediction_idx, f"Unknown_{prediction_idx}"),
                "probabilities": probabilities.tolist(),
                "confidence": float(probabilities[prediction_idx]),
                "processing_time_ms": processing_time * 1000
            }
            
        except Exception as e:
            logger.error(f"Error in multiclass prediction: {e}")
            return {"error": f"Multiclass prediction failed: {e}"}
    
    def detect_anomaly(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Complete anomaly detection pipeline
        
        Args:
            data: Input data dictionary
            
        Returns:
            Dictionary containing complete detection results
        """
        start_time = time.time()
        
        # Binary classification first
        binary_result = self.predict_binary(data)
        if "error" in binary_result:
            return binary_result
        
        result = {
            "timestamp": time.time(),
            "binary_prediction": binary_result["prediction"],
            "binary_confidence": binary_result["confidence"],
            "binary_probabilities": binary_result["probabilities"],
            "is_anomaly": binary_result["is_anomaly"],
            "multiclass_prediction": None,
            "multiclass_confidence": None,
            "attack_type": "Normal"
        }
        
        # If anomaly detected, perform multiclass classification
        if binary_result["is_anomaly"]:
            multiclass_result = self.predict_multiclass(data)
            if "error" not in multiclass_result:
                result.update({
                    "multiclass_prediction": multiclass_result["prediction_index"],
                    "multiclass_confidence": multiclass_result["confidence"],
                    "multiclass_probabilities": multiclass_result["probabilities"],
                    "attack_type": multiclass_result["prediction_label"]
                })
        
        # Update overall stats
        total_time = time.time() - start_time
        self.stats["total_predictions"] += 1
        self.stats["total_time"] += total_time
        self.stats["avg_time_per_prediction"] = self.stats["total_time"] / self.stats["total_predictions"]
        
        result["total_processing_time_ms"] = total_time * 1000
        
        return result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        return self.stats.copy()
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.stats = {
            "total_predictions": 0,
            "binary_predictions": 0,
            "multiclass_predictions": 0,
            "total_time": 0.0,
            "avg_time_per_prediction": 0.0
        }
