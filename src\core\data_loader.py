"""
Data Loading and Streaming Module
Handles 5G-NIDD dataset loading and streaming functionality
"""

import pandas as pd
import numpy as np
import os
import logging
from typing import Optional, Generator, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class DataLoader:
    """
    Handles loading and streaming of 5G-NIDD dataset
    """
    
    def __init__(self, data_path: str):
        """
        Initialize DataLoader with dataset path
        
        Args:
            data_path: Path to the CSV dataset file
        """
        self.data_path = Path(data_path)
        self.df: Optional[pd.DataFrame] = None
        self.current_index = 0
        
        if self.data_path.exists():
            self.load_data()
        else:
            logger.error(f"Dataset not found at: {data_path}")
    
    def load_data(self) -> bool:
        """
        Load the dataset from CSV file
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Loading dataset from: {self.data_path}")
            self.df = pd.read_csv(self.data_path)
            logger.info(f"Dataset loaded successfully. Shape: {self.df.shape}")
            return True
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            return False
    
    def get_batch(self, batch_size: int) -> Optional[pd.DataFrame]:
        """
        Get a batch of records from the dataset
        
        Args:
            batch_size: Number of records to return
            
        Returns:
            DataFrame with batch_size records or None if no data
        """
        if self.df is None or self.df.empty:
            return None
        
        if self.current_index >= len(self.df):
            self.current_index = 0  # Reset to beginning
        
        end_index = min(self.current_index + batch_size, len(self.df))
        batch = self.df.iloc[self.current_index:end_index].copy()
        self.current_index = end_index
        
        return batch
    
    def stream_records(self, max_records: Optional[int] = None) -> Generator[Dict[str, Any], None, None]:
        """
        Stream records one by one
        
        Args:
            max_records: Maximum number of records to stream (None for all)
            
        Yields:
            Dict containing record data
        """
        if self.df is None or self.df.empty:
            return
        
        count = 0
        for idx, row in self.df.iterrows():
            if max_records and count >= max_records:
                break
            
            yield row.to_dict()
            count += 1
    
    def reset_stream(self):
        """Reset the streaming position to the beginning"""
        self.current_index = 0
    
    def get_dataset_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded dataset
        
        Returns:
            Dict containing dataset statistics
        """
        if self.df is None:
            return {"status": "No data loaded"}
        
        return {
            "total_records": len(self.df),
            "columns": list(self.df.columns),
            "shape": self.df.shape,
            "memory_usage": self.df.memory_usage(deep=True).sum(),
            "has_malicious_label": "labelmalicious" in self.df.columns,
            "current_position": self.current_index
        }
