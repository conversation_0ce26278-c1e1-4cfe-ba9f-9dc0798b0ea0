import streamlit as st
import pandas as pd
import os
import time 
import sys 
import datetime # For processing_timestamps if used
import logging
import logging.handlers 
import json # For pretty printing dicts if st.json is not enough
import uuid
import plotly.graph_objects as go # For donut chart and potentially time series

# --- Logging Configuration ---
LOG_FILENAME = "continuum5g_insight_run.log" 

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILENAME, mode='a'), 
    ]
)

dashboard_logger = logging.getLogger(__name__) 
dashboard_logger.info("Dashboard script started/reloaded.")
# --- End Logging Configuration ---


# Adjust sys.path to include the \'continuum5g_insight_project\' directory
current_script_dir = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = current_script_dir 

if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

try:
    from continuum5g_insight.agents.orchestration_agent import OrchestrationAgent
    from continuum5g_insight.agents.data_streaming_agent import DataStreamer
    from continuum5g_insight.agents.rapids_preprocessing_agent import RAPIDSPreprocessingAgent
    from continuum5g_insight.agents.high_volume_orchestrator import HighVolumeProcessingOrchestrator
    from continuum5g_insight.core.utils import get_gpu_device_info, is_cudf_available, select_optimal_device
except ModuleNotFoundError as e:
    dashboard_logger.error(f"Failed to import Continuum5G Insight components: {e}. Ensure dashboard.py is in \'continuum5g_insight_project\' root and package is correct.")
    st.error(f"Continuum5G Insight components not found: {e}. Ensure dashboard.py is in the \'continuum5g_insight_project\' root directory and the continuum5g_insight package is structured correctly.")
    st.stop() 


# Page Config
st.set_page_config(page_title="Continuum5G Insight Dashboard", layout="wide", initial_sidebar_state="expanded")

# --- GPU Hardware Detection ---
@st.cache_data(ttl=3600)  # Cache for 1 hour
def get_system_capabilities():
    """Get system GPU capabilities and NVIDIA technology availability"""
    gpu_info = get_gpu_device_info()
    cudf_available = is_cudf_available()
    optimal_device = select_optimal_device()
    
    return {
        "gpu_info": gpu_info,
        "cudf_available": cudf_available,
        "optimal_device": optimal_device,
        "nvidia_acceleration": gpu_info["device_count"] > 0 and cudf_available
    }

system_caps = get_system_capabilities()

# --- Helper function to format SLM outputs (keep for detailed analysis view) ---
def format_slm_output(slm_data, title="SLM Analysis"):
    if not slm_data or not isinstance(slm_data, dict): # Added isinstance check
        st.markdown(f"**{title}:** No data available or data is not in the expected format.")
        return
    
    st.markdown(f"**{title}:**")
    # Check for common keys and display them nicely
    if "summary" in slm_data:
        st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;**Summary:** {slm_data['summary']}")
    if "threat_level_assessment" in slm_data:
        level = slm_data['threat_level_assessment']
        color = "red" if level in ["High", "Critical"] else ("orange" if level == "Medium" else "green")
        st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;**Threat Level:** <span style='color:{color}; font-weight:bold;'>{level}</span>", unsafe_allow_html=True)
    if "confidence_score" in slm_data:
        st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;**Confidence:** {slm_data['confidence_score']:.2f}")
    
    if "strategy_name" in slm_data: # For Response SLM
        st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;**Strategy Name:** {slm_data['strategy_name']}")
    if "overall_goal" in slm_data:
        st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;**Overall Goal:** {slm_data['overall_goal']}")
    
    if "recommended_actions_summary" in slm_data and slm_data["recommended_actions_summary"]:
        st.markdown("&nbsp;&nbsp;&nbsp;&nbsp;**Recommended Actions Summary:**")
        for action in slm_data["recommended_actions_summary"]:
            st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- {action}")
            
    if "suggested_steps" in slm_data and slm_data["suggested_steps"]: # For Response SLM
        st.markdown("&nbsp;&nbsp;&nbsp;&nbsp;**Suggested Steps:**")
        for step in slm_data["suggested_steps"]:
            st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- **Action:** {step.get('action', 'N/A')} "
                        f"(Priority: {step.get('priority', 'N/A')}, Category: {step.get('category', 'N/A')})")

    if "correlation_hypotheses" in slm_data and slm_data["correlation_hypotheses"]:
        st.markdown("&nbsp;&nbsp;&nbsp;&nbsp;**Correlation Hypotheses:**")
        for hypothesis in slm_data["correlation_hypotheses"]:
            st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;- {hypothesis}")
            
    if "escalation_criteria" in slm_data: # For Response SLM
        st.markdown(f"&nbsp;&nbsp;&nbsp;&nbsp;**Escalation Criteria:** {slm_data['escalation_criteria']}")

    with st.popover("View Full SLM JSON Output"):
        st.json(slm_data)
# --- End Helper function ---


# Main UI Structure
st.title("🛡️ Continuum5G Insight - 5G Security Monitor")
st.markdown("Live dashboard for monitoring 5G network security with **NVIDIA GPU Acceleration** 🚀")

# GPU Status Banner
gpu_status_col1, gpu_status_col2, gpu_status_col3 = st.columns([1, 2, 1])
with gpu_status_col2:
    if system_caps["nvidia_acceleration"]:
        st.success(f"🚀 NVIDIA GPU Acceleration Enabled - {system_caps['gpu_info']['device_count']} GPU(s) Available")
        if system_caps['gpu_info']['devices']:
            primary_gpu = system_caps['gpu_info']['devices'][0]
            memory_gb = primary_gpu['total_memory_mb'] / 1024
            st.caption(f"Primary GPU: {primary_gpu['name']} ({memory_gb:.1f} GB)")
    else:
        st.warning("⚠️ Running in CPU Mode - Install NVIDIA drivers and RAPIDS for GPU acceleration")

st.markdown("---")

# Sidebar
st.sidebar.header("⚙️ Controls & Configuration")

# File Uploader/Path
uploaded_file = st.sidebar.file_uploader("Upload 5G-NIDD CSV (Optional)", type=["csv"])
default_csv_path = os.path.join(PROJECT_ROOT, "data", "sample_inputs", "5g_nidd_dataset.csv.placeholder")
st.sidebar.caption(f"Default dataset: `./data/sample_inputs/5g_nidd_dataset.csv` (if placeholder is replaced)")

st.sidebar.markdown("---")

# NVIDIA GPU Acceleration Settings
st.sidebar.subheader("🚀 NVIDIA Acceleration")
processing_strategy = st.sidebar.selectbox(
    "Processing Strategy",
    ["Auto-Select", "GPU-Accelerated", "Hybrid (GPU+CPU)", "CPU-Only"],
    index=0 if system_caps["nvidia_acceleration"] else 3,
    help="Choose processing strategy based on available hardware"
)

if system_caps["nvidia_acceleration"]:
    enable_rapids_preprocessing = st.sidebar.checkbox(
        "Enable RAPIDS Preprocessing", 
        value=True,
        help="Use GPU-accelerated data preprocessing with cuDF"
    )
    enable_gpu_inference = st.sidebar.checkbox(
        "Enable GPU Inference",
        value=True, 
        help="Use cuML for GPU-accelerated anomaly detection"
    )
else:
    enable_rapids_preprocessing = False
    enable_gpu_inference = False
    st.sidebar.info("NVIDIA GPU not available - running in CPU mode")

batch_size = st.sidebar.slider(
    "Batch Size", 
    1, 
    1000, 
    100 if system_caps["nvidia_acceleration"] else 10,
    help="Larger batches can improve GPU utilization"
)

st.sidebar.markdown("---")
# Critical Reminders in an expander
with st.sidebar.expander("⚠️ Important Setup Instructions", expanded=False):
    st.info(f"Ensure models & scaler are in: `{os.path.join('models', 'rf_models')}` (relative to project root).")
    st.warning("CRITICAL: Customize `feature_columns` in `continuum5g_insight/agents/anomaly_detection_agent.py` for accurate predictions based on your model training!")
    st.caption("Placeholder models and data will run but may not yield meaningful security insights.")
st.sidebar.markdown("---")


# Controls
processing_mode = st.sidebar.radio(
    "Processing Mode",
    ("Manual Batch", "Continuous Stream (Simulated)"),
    key="processing_mode",
    help="Manual: Process a defined number of records. Continuous: Process records sequentially with a delay to simulate a stream."
)

# Requests per Second - now common for both modes
requests_per_second = st.sidebar.slider(
    "Requests per Second (approx)", 
    0.1, 
    100.0, 
    10.0,  # Default to 10 RPS
    0.1, 
    key="requests_per_second", 
    help="Adjusts delay between processing records. Applies to both modes."
)

if st.session_state.processing_mode == "Continuous Stream (Simulated)":
    max_records_to_process_continuous = st.sidebar.number_input(
        "Max Records (stops if duration or records met)", min_value=1, max_value=1000000, value=1000, key="max_records_continuous"
    )
    run_duration_minutes_input = st.sidebar.number_input(
        "Run Duration (minutes, 0 for indefinite)", min_value=0, value=st.session_state.get('run_duration_minutes', 0), key="run_duration_minutes"
    )
else:
    max_records_to_process_manual = st.sidebar.number_input(
        "Number of records to process in batch", min_value=1, max_value=100000, value=100, key="max_records_manual"
    )

run_button_text = "🚀 Start Streaming" if st.session_state.processing_mode == "Continuous Stream (Simulated)" else "🚀 Process Batch"
run_button = st.sidebar.button(run_button_text, use_container_width=True)
clear_button = st.sidebar.button("🧹 Clear Run Output & State", use_container_width=True)

st.sidebar.markdown("---")
st.sidebar.subheader("📊 Live Run Stats")
processed_requests_sidebar_placeholder = st.sidebar.empty()
blocked_requests_sidebar_placeholder = st.sidebar.empty()
actual_malicious_sidebar_placeholder = st.sidebar.empty()  # For actual malicious count


# About Section
st.sidebar.markdown("---")
st.sidebar.markdown("### About Continuum5G Insight")
st.sidebar.markdown("This dashboard demonstrates a multi-agent cybersecurity system leveraging **NVIDIA GPU acceleration** with RAPIDS cuDF/cuML, RandomForest models, and simulated Small Language Models (SLMs) with RAG for 5G network security.")
st.sidebar.markdown("**Technologies:** NVIDIA RAPIDS, cuML, cuDF, RandomForest, SLMs, RAG")

# NVIDIA GPU Information
if system_caps["nvidia_acceleration"]:
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🚀 NVIDIA GPU Information")
    
    with st.sidebar.expander("GPU Details", expanded=False):
        for i, gpu in enumerate(system_caps["gpu_info"]["devices"]):
            st.write(f"**GPU {i}:** {gpu['name']}")
            memory_total_gb = gpu['total_memory_mb'] / 1024
            memory_used_gb = gpu['used_memory_mb'] / 1024
            memory_free_gb = gpu['free_memory_mb'] / 1024
            st.write(f"- Total Memory: {memory_total_gb:.1f} GB")
            st.write(f"- Used Memory: {memory_used_gb:.1f} GB ({(memory_used_gb/memory_total_gb)*100:.1f}%)")
            st.write(f"- Free Memory: {memory_free_gb:.1f} GB")
    
    st.sidebar.markdown(f"**RAPIDS cuDF:** {'✅ Available' if system_caps['cudf_available'] else '❌ Not Available'}")
    device_type, device_info = system_caps['optimal_device']
    st.sidebar.markdown(f"**Optimal Device:** {device_type.upper()}")
    
    # Performance Summary
    if st.session_state.gpu_performance_stats["throughput_records_per_sec"]:
        avg_throughput = sum(st.session_state.gpu_performance_stats["throughput_records_per_sec"][-10:]) / len(st.session_state.gpu_performance_stats["throughput_records_per_sec"][-10:])
        st.sidebar.markdown(f"**Avg Throughput:** {avg_throughput:.1f} req/s")
        
    if st.session_state.gpu_performance_stats["batch_processing_time"]:
        avg_batch_time = sum(st.session_state.gpu_performance_stats["batch_processing_time"][-10:]) / len(st.session_state.gpu_performance_stats["batch_processing_time"][-10:])
        st.sidebar.markdown(f"**Avg Batch Time:** {avg_batch_time*1000:.1f} ms")

# Initialize session state variables if not already present
if 'processing_mode' not in st.session_state: # Add this line
    st.session_state.processing_mode = "Manual Batch" # Default value
if 'requests_per_second' not in st.session_state: # Initialize if not present
    st.session_state.requests_per_second = 10.0

if 'run_active' not in st.session_state:
    st.session_state.run_active = False # To manage continuous run stop
if 'firewall_logs' not in st.session_state:
    st.session_state.firewall_logs = []
if 'total_requests_count' not in st.session_state: # Overall total, might be different from a single run's processed count
    st.session_state.total_requests_count = 0
if 'blocked_inputs_count' not in st.session_state: # Overall blocked
    st.session_state.blocked_inputs_count = 0
if 'processed_requests_current_run' not in st.session_state:
    st.session_state.processed_requests_current_run = 0
if 'blocked_requests_current_run' not in st.session_state:
    st.session_state.blocked_requests_current_run = 0
if 'actual_malicious_count_current_run' not in st.session_state:
    st.session_state.actual_malicious_count_current_run = 0
if 'actual_malicious_present_in_csv' not in st.session_state:
    st.session_state.actual_malicious_present_in_csv = False

if 'requests_over_time_data' not in st.session_state: # Stores {'time': [], 'count': []}
    st.session_state.requests_over_time_data = {'time': [], 'cumulative_count': [], 'blocked_count': [], 'allowed_count': []}
if 'firewall_flagged_stats' not in st.session_state: # For donut chart
    st.session_state.firewall_flagged_stats = {"Allowed": 0, "Blocked": 0}
if 'run_duration_minutes' not in st.session_state:
    st.session_state.run_duration_minutes = 0

# GPU Performance Metrics
if 'gpu_performance_stats' not in st.session_state:
    st.session_state.gpu_performance_stats = {
        "preprocessing_time": [],
        "inference_time": [],
        "throughput_records_per_sec": [],
        "gpu_memory_usage": [],
        "batch_processing_time": []
    }
if 'processing_device_stats' not in st.session_state:
    st.session_state.processing_device_stats = {"GPU": 0, "CPU": 0, "Hybrid": 0}

# Current throughput tracking
if 'current_throughput' not in st.session_state:
    st.session_state.current_throughput = 0.0
if 'last_second_timestamp' not in st.session_state:
    st.session_state.last_second_timestamp = time.time()
if 'records_this_second' not in st.session_state:
    st.session_state.records_this_second = 0

# Updated MULTICLASS_THREAT_TYPES for 5G Network Security Context
MULTICLASS_THREAT_TYPES = [
    'PortScan', 'DDoS_Attempt', 'Malware', 'Intrusion', 
    'Data_Leak', 'Anomalous_Flow', 'Unknown_Threat'
]
MAX_FIREWALL_LOG_ENTRIES = 200 # Cap for display performance


# Main Area Layout
# Row 1: GPU Performance Metrics (if GPU acceleration is available)
if system_caps["nvidia_acceleration"]:
    st.subheader("⚡ NVIDIA GPU Performance Metrics")
    gpu_col1, gpu_col2, gpu_col3, gpu_col4 = st.columns(4)
    
    with gpu_col1:
        st.metric("GPU Memory Usage", "0 GB", help="Current GPU memory utilization")
        gpu_memory_placeholder = st.empty()
    
    with gpu_col2:
        st.metric("Processing Throughput", "0 req/s", help="Records processed per second")
        throughput_placeholder = st.empty()
    
    with gpu_col3:
        st.metric("Avg Processing Time", "0 ms", help="Average time per batch")
        processing_time_placeholder = st.empty()
    
    with gpu_col4:
        st.metric("Device Utilization", "CPU", help="Primary processing device")
        device_utilization_placeholder = st.empty()
    
    st.markdown("---")

# Row 2: Charts and KPIs
col_chart, col_kpis, col_donut = st.columns([2.5, 1, 1.5]) # New layout: Wider chart, stacked KPIs

with col_chart:
    st.subheader("Total Requests Over Time")
    total_requests_time_chart_placeholder = st.empty()

with col_kpis:
    st.subheader("Total Requests")
    total_requests_kpi_placeholder = st.empty()
    st.subheader("Blocked Inputs")
    blocked_inputs_kpi_placeholder = st.empty()
    # Current Throughput with tight dashed border
    current_throughput_kpi_placeholder = st.empty()

with col_donut:
    st.subheader("Firewall Flagged Status")
    firewall_donut_chart_placeholder = st.empty()

st.markdown("---")

# Row 2: Firewall Logs
st.subheader("Firewall Logs")
firewall_logs_placeholder = st.empty()

st.markdown("---")

# Row 3: User Input and System Analysis (mimicking idea.jpeg)
col_input, col_analysis = st.columns(2)

with col_input:
    st.subheader("Current User Input (CSV Row)")
    current_input_placeholder = st.empty()

with col_analysis:
    st.subheader("Current System Analysis")
    # Placeholders for structured analysis display
    detection_status_placeholder = st.empty()
    attack_type_placeholder = st.empty()
    threat_analysis_slm_placeholder = st.empty()
    response_strategy_slm_placeholder = st.empty()
    raw_analysis_popover_placeholder = st.empty()


# --- Helper function to update KPIs and Charts ---
def update_display(input_data):
    # KPIs in main area
    total_requests_kpi_placeholder.metric(label="Total Requests (All Runs)", value=f"{st.session_state.total_requests_count:,}")
    blocked_inputs_kpi_placeholder.metric(label="Blocked Inputs (All Runs)", value=f"{st.session_state.blocked_inputs_count:,}")
    
    # Current Throughput with tight dashed border around content
    current_throughput_html = f"""
    <div style="border: 2px dashed #0066cc; padding: 8px; border-radius: 5px; margin: 5px 0; display: inline-block;">
        <div style="font-size: 0.875rem; font-weight: 600; color: rgb(49, 51, 63); margin-bottom: 2px;">Current Throughput</div>
        <div style="font-size: 1.875rem; font-weight: 600; color: rgb(49, 51, 63);">{st.session_state.current_throughput:.1f} req/s</div>
        <div style="font-size: 0.875rem; color: rgb(34, 139, 34);">▲ {st.session_state.current_throughput:.1f}</div>
    </div>
    """
    current_throughput_kpi_placeholder.markdown(current_throughput_html, unsafe_allow_html=True)

    # GPU Performance Metrics (if available)
    if system_caps["nvidia_acceleration"]:
        gpu_stats = st.session_state.gpu_performance_stats
        device_stats = st.session_state.processing_device_stats
        
        # Update GPU memory usage
        if gpu_stats["gpu_memory_usage"]:
            avg_memory = sum(gpu_stats["gpu_memory_usage"][-10:]) / len(gpu_stats["gpu_memory_usage"][-10:])
            gpu_memory_placeholder.metric("GPU Memory Usage", f"{avg_memory:.1f} GB")
        
        # Update throughput
        if gpu_stats["throughput_records_per_sec"]:
            avg_throughput = sum(gpu_stats["throughput_records_per_sec"][-10:]) / len(gpu_stats["throughput_records_per_sec"][-10:])
            throughput_placeholder.metric("Processing Throughput", f"{avg_throughput:.1f} req/s")
        
        # Update processing time
        if gpu_stats["batch_processing_time"]:
            avg_time = sum(gpu_stats["batch_processing_time"][-10:]) / len(gpu_stats["batch_processing_time"][-10:]) * 1000
            processing_time_placeholder.metric("Avg Processing Time", f"{avg_time:.1f} ms")
        
        # Update device utilization
        total_processed = sum(device_stats.values())
        if total_processed > 0:
            primary_device = max(device_stats, key=device_stats.get)
            device_utilization_placeholder.metric("Device Utilization", primary_device)

    # Live run stats in sidebar
    processed_requests_sidebar_placeholder.metric(label="Processed Requests (Current Run)", value=f"{st.session_state.processed_requests_current_run:,}")
    blocked_requests_sidebar_placeholder.metric(label="Blocked Requests (Model)", value=f"{st.session_state.blocked_requests_current_run:,}")
    # Determine label for Actual Malicious metric
    if 'labelmalicious' in input_data.columns:
        actual_malicious_label = "Actual Malicious (CSV Label - Supported)"
    else:
        actual_malicious_label = "Actual Malicious (CSV Label)"
    if st.session_state.actual_malicious_present_in_csv:
        actual_malicious_sidebar_placeholder.metric(label=actual_malicious_label, value=f"{st.session_state.actual_malicious_count_current_run:,}")
    else:
        actual_malicious_sidebar_placeholder.empty()

    # Time Series Chart for Total Requests
    if st.session_state.requests_over_time_data['time']:
        df_time_series = pd.DataFrame(st.session_state.requests_over_time_data)
        
        # Create Plotly figure with multiple trend lines
        fig_time_series = go.Figure()
        
        # Main cumulative line (blue)
        fig_time_series.add_trace(go.Scatter(
            x=df_time_series['time'],
            y=df_time_series['cumulative_count'],
            mode='lines',
            name='Total Requests',
            line=dict(color='blue', width=6),
            showlegend=True
        ))
        
        # Allowed requests trend line (green, thin)
        fig_time_series.add_trace(go.Scatter(
            x=df_time_series['time'],
            y=df_time_series['allowed_count'],
            mode='lines',
            name='Allowed Requests',
            line=dict(color='green', width=1),
            showlegend=True
        ))
        
        # Blocked requests trend line (red, thin)
        fig_time_series.add_trace(go.Scatter(
            x=df_time_series['time'],
            y=df_time_series['blocked_count'],
            mode='lines',
            name='Blocked Requests',
            line=dict(color='red', width=1),
            showlegend=True
        ))
        
        # Update layout
        fig_time_series.update_layout(
            title="Total Requests Over Time",
            xaxis_title="Time",
            yaxis_title="Request Count",
            margin=dict(t=40, b=40, l=40, r=40),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        total_requests_time_chart_placeholder.plotly_chart(fig_time_series, use_container_width=True, key=f"time_series_chart_{str(uuid.uuid4())}")
    else:
        total_requests_time_chart_placeholder.empty() # Or show a "no data" message

    # Donut Chart for Firewall Flagged Status
    labels = list(st.session_state.firewall_flagged_stats.keys())
    values = list(st.session_state.firewall_flagged_stats.values())
    if sum(values) > 0: # Only draw if there's data
        fig_donut = go.Figure(data=[go.Pie(labels=labels, values=values, hole=.6,
                                           marker_colors=['green', 'red'] if labels == ["Allowed", "Blocked"] else None)])
        fig_donut.update_layout(margin=dict(t=0, b=0, l=0, r=0), showlegend=True)
        firewall_donut_chart_placeholder.plotly_chart(fig_donut, use_container_width=True, key=f"donut_chart_with_data_{str(uuid.uuid4())}")
    else:
        # Display an informational message if no data
        firewall_donut_chart_placeholder.info("No data available for Firewall Flagged Status.")


    # Firewall Logs Table
    if st.session_state.firewall_logs:
        # Display most recent logs first, and limit the number of logs for performance
        display_logs = st.session_state.firewall_logs[-MAX_FIREWALL_LOG_ENTRIES:]
        df_logs = pd.DataFrame(display_logs)
        
        # Define the desired order of columns for the firewall log
        log_columns_ordered = [
            'Timestamp', 
            'Firewall Flagged', 
            'Binary Prediction',
            'Anomaly Confidence',
            'Multiclass Prediction',
            'Threat Level (SLM)',
            'SLM Response Summary'
        ]
        
        # Ensure all defined columns exist in df_logs, add missing ones with N/A or default
        for col in log_columns_ordered:
            if col not in df_logs.columns:
                df_logs[col] = "N/A" # Or appropriate default

        df_logs_display = df_logs[log_columns_ordered]


        # Apply styling for "Blocked" rows
        def style_blocked_rows(row):
            style = [''] * len(row) # Default no style
            if row["Firewall Flagged"] == "Blocked":
                # Apply style only to the 'Firewall Flagged' column cell
                try:
                    idx = list(row.index).index("Firewall Flagged")
                    style[idx] = 'background-color: #FFCDD2; color: black;' # Light Red background, black text
                except ValueError:
                    pass # Column not found
            elif row["Firewall Flagged"] == "Allowed":
                try:
                    idx = list(row.index).index("Firewall Flagged")
                    style[idx] = 'background-color: #C8E6C9; color: black;' # Light Green background, black text
                except ValueError:
                    pass # Column not found
            return style

        # Use st.dataframe for better control, or st.data_editor if edits are needed (not here)
        firewall_logs_placeholder.dataframe(
            df_logs_display.style.apply(style_blocked_rows, axis=1),
            height=300, # Adjust height as needed
            use_container_width=True
        )
    else:
        firewall_logs_placeholder.info("No firewall logs to display yet.")

# Initial display on load
update_display(pd.DataFrame()) # Pass an empty DataFrame for initial load
# --- End Helper function ---

# --- Helper function to update current throughput ---
def update_current_throughput():
    """Update current throughput based on records processed in the last second"""
    current_time = time.time()
    time_diff = current_time - st.session_state.last_second_timestamp
    
    if time_diff >= 1.0:  # At least 1 second has passed
        # Calculate throughput for the last second
        st.session_state.current_throughput = st.session_state.records_this_second / time_diff
        
        # Reset counters for next second
        st.session_state.records_this_second = 0
        st.session_state.last_second_timestamp = current_time
    else:
        # Still within the same second, increment counter
        st.session_state.records_this_second += 1
# --- End Helper function ---

# --- Function to read log file (for system activity, if still needed) ---
def get_last_n_log_lines(log_file, n=50):
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        return "".join(lines[-n:])
    except FileNotFoundError:
        # dashboard_logger.warning(f"Log file '{log_file}' not found.") # Less verbose for UI
        return "Log file not found yet."
    except Exception as e:
        dashboard_logger.error(f"Error reading log file '{log_file}': {e}")
        return f"Error reading log file: {e}"

# --- Helper function to process detection results ---
def process_detection_result(record_dict_full_row, detection_alert, current_timestamp, orchestrator):
    """Process a single detection result and update UI components"""
    is_blocked = False
    binary_proba_anomaly = 0.0
    binary_prediction_val = "N/A"

    # Increment actual malicious count if applicable
    if st.session_state.actual_malicious_present_in_csv:
        if record_dict_full_row.get('labelmalicious') == 1:
            st.session_state.actual_malicious_count_current_run += 1

    if detection_alert:
        # Get P(anomaly) for display purposes
        if 'binary_proba' in detection_alert and \
           detection_alert['binary_proba'] is not None and \
           isinstance(detection_alert['binary_proba'], list) and \
           len(detection_alert['binary_proba']) == 2:
            binary_proba_anomaly = detection_alert['binary_proba'][1] 
        elif 'binary_proba' in detection_alert and \
             detection_alert['binary_proba'] is not None and \
             isinstance(detection_alert['binary_proba'], list) and \
             len(detection_alert['binary_proba']) == 1:
            binary_proba_anomaly = detection_alert['binary_proba'][0]

        # Determine 'is_blocked' based on the agent's 'binary_prediction'
        if 'binary_prediction' in detection_alert and \
           detection_alert['binary_prediction'] is not None:
            is_blocked = (detection_alert['binary_prediction'] == 1)
            binary_prediction_val = detection_alert['binary_prediction']
    
    firewall_log_entry = {
        "Timestamp": current_timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
        "Firewall Flagged": "Blocked" if is_blocked else "Allowed",
        "Binary Prediction": binary_prediction_val,
        "Anomaly Confidence": f"{binary_proba_anomaly:.2%}",
        "Multiclass Prediction": "N/A",
        "Threat Level (SLM)": "N/A",
        "SLM Response Summary": "N/A"
    }

    analysis_display_content = {}
    
    # Clear previous analysis details
    detection_status_placeholder.empty()
    attack_type_placeholder.empty()
    threat_analysis_slm_placeholder.empty()
    response_strategy_slm_placeholder.empty()
    raw_analysis_popover_placeholder.empty()

    detection_status_str = "Blocked" if is_blocked else "Allowed"
    detection_status_placeholder.markdown(f"**Detection Status:** {detection_status_str} (Confidence: {binary_proba_anomaly:.2%})")
    analysis_display_content["Detection Status"] = detection_status_str
    analysis_display_content["Anomaly Confidence (Binary Model)"] = f"{binary_proba_anomaly:.2%}"

    if is_blocked:
        st.session_state.blocked_inputs_count += 1
        st.session_state.blocked_requests_current_run += 1
        st.session_state.firewall_flagged_stats["Blocked"] += 1
        
        multiclass_pred = detection_alert.get('multiclass_prediction', 'Unknown_Threat')
        firewall_log_entry["Multiclass Prediction"] = multiclass_pred
        attack_type_placeholder.markdown(f"**Attack Type (Multiclass Model):** {multiclass_pred}")
        analysis_display_content["Attack Type (Multiclass Model)"] = multiclass_pred

        enriched_alert = orchestrator.threat_analyzer.analyze_threat(detection_alert) 
        final_output = orchestrator.response_strategist.generate_response_strategy(enriched_alert)
        
        slm_threat_analysis = enriched_alert.get("slm_analysis", {})
        slm_response_strategy = final_output.get("slm_generated_strategy", {})

        firewall_log_entry["Threat Level (SLM)"] = slm_threat_analysis.get('threat_level_assessment', 'N/A')
        response_summary_for_log = slm_response_strategy.get('summary', 
                                                          slm_response_strategy.get('overall_goal', 'N/A'))
        if isinstance(response_summary_for_log, list):
            response_summary_for_log = "; ".join(response_summary_for_log[:2]) + ("..." if len(response_summary_for_log) > 2 else "")

        firewall_log_entry["SLM Response Summary"] = response_summary_for_log

        with threat_analysis_slm_placeholder.container():
            format_slm_output(slm_threat_analysis, title="Threat Analysis (Simulated SLM)")
        with response_strategy_slm_placeholder.container():
            format_slm_output(slm_response_strategy, title="Response Strategy (Simulated SLM)")
        
        analysis_display_content["Threat Analysis (Simulated SLM)"] = slm_threat_analysis
        analysis_display_content["Response Strategy (Simulated SLM)"] = slm_response_strategy

    else:
        st.session_state.firewall_flagged_stats["Allowed"] += 1
        attack_type_placeholder.markdown("**Attack Type (Multiclass Model):** N/A (Allowed)")
        threat_analysis_slm_placeholder.info("Request allowed, no further AI-driven threat analysis performed by SLMs for this event.")
        analysis_display_content["Details"] = "Request allowed, no further threat analysis."
    
    with raw_analysis_popover_placeholder.popover("View Full Analysis JSON"):
        st.json(analysis_display_content)

    st.session_state.firewall_logs.append(firewall_log_entry)
    # Cap the firewall_logs list to prevent excessive memory usage
    if len(st.session_state.firewall_logs) > MAX_FIREWALL_LOG_ENTRIES + 50:
        st.session_state.firewall_logs = st.session_state.firewall_logs[-MAX_FIREWALL_LOG_ENTRIES:]
    
    # Update time series data
    st.session_state.requests_over_time_data['time'].append(current_timestamp)
    st.session_state.requests_over_time_data['cumulative_count'].append(st.session_state.total_requests_count)
    
    # Add blocked/allowed counts for trend lines
    current_blocked_count = sum(1 for log in st.session_state.firewall_logs if log.get('Firewall Flagged') == 'Blocked')
    current_allowed_count = sum(1 for log in st.session_state.firewall_logs if log.get('Firewall Flagged') == 'Allowed')
    st.session_state.requests_over_time_data['blocked_count'].append(current_blocked_count)
    st.session_state.requests_over_time_data['allowed_count'].append(current_allowed_count)
    
    # Keep data to a manageable size for plotting
    max_time_series_points = 500 
    if len(st.session_state.requests_over_time_data['time']) > max_time_series_points:
        st.session_state.requests_over_time_data['time'].pop(0)
        st.session_state.requests_over_time_data['cumulative_count'].pop(0)
        st.session_state.requests_over_time_data['blocked_count'].pop(0)
        st.session_state.requests_over_time_data['allowed_count'].pop(0)
# --- End Helper function ---
# --- End Function to read log file ---


if clear_button:
    dashboard_logger.info("Clear Run Output button clicked.")
    st.session_state.firewall_logs = []
    st.session_state.total_requests_count = 0 # Keep overall totals or reset? For now, reset all.
    st.session_state.blocked_inputs_count = 0
    st.session_state.processed_requests_current_run = 0
    st.session_state.blocked_requests_current_run = 0
    st.session_state.requests_over_time_data = {'time': [], 'cumulative_count': [], 'blocked_count': [], 'allowed_count': []}
    st.session_state.firewall_flagged_stats = {"Allowed": 0, "Blocked": 0}
    st.session_state.run_active = False
    st.session_state.actual_malicious_count_current_run = 0
    st.session_state.actual_malicious_present_in_csv = False
    
    # Reset GPU performance stats
    st.session_state.gpu_performance_stats = {
        "preprocessing_time": [],
        "inference_time": [],
        "throughput_records_per_sec": [],
        "gpu_memory_usage": [],
        "batch_processing_time": []
    }
    st.session_state.processing_device_stats = {"GPU": 0, "CPU": 0, "Hybrid": 0}
    
    # Reset current throughput tracking
    st.session_state.current_throughput = 0.0
    st.session_state.last_second_timestamp = time.time()
    st.session_state.records_this_second = 0
    
    current_input_placeholder.empty()
    # Clear structured analysis placeholders
    detection_status_placeholder.empty()
    attack_type_placeholder.empty()
    threat_analysis_slm_placeholder.empty()
    response_strategy_slm_placeholder.empty()
    raw_analysis_popover_placeholder.empty()

    update_display(pd.DataFrame()) # Update to show empty state
    st.rerun()

if run_button:
    dashboard_logger.info(f"{run_button_text} clicked.")
    
    # Clear previous live displays for the new run
    current_input_placeholder.empty()
    detection_status_placeholder.empty()
    attack_type_placeholder.empty()
    threat_analysis_slm_placeholder.empty()
    response_strategy_slm_placeholder.empty()
    raw_analysis_popover_placeholder.empty()
        
    # Reset counts for the new run specifically
    st.session_state.processed_requests_current_run = 0
    st.session_state.blocked_requests_current_run = 0
    st.session_state.actual_malicious_count_current_run = 0
    st.session_state.actual_malicious_present_in_csv = False
    # Optionally reset firewall_logs and firewall_flagged_stats for a "clean slate" view per run,
    # or let them accumulate across runs if that's desired. For now, let's clear them for each run.
    st.session_state.firewall_logs = []
    st.session_state.firewall_flagged_stats = {"Allowed": 0, "Blocked": 0} 
    # requests_over_time_data could also be reset if the chart should only reflect the current run
    # st.session_state.requests_over_time_data = {'time': [], 'cumulative_count': []} # Uncomment to reset chart per run

    update_display(pd.DataFrame()) # Show initial empty state before processing

    # status_placeholder.info("Initializing Continuum5G Insight system... Please wait.")
    dashboard_logger.info("Initializing Continuum5G Insight system...")
    dashboard_logger.info(f"🔧 System Configuration:")
    dashboard_logger.info(f"   - NVIDIA Acceleration: {system_caps['nvidia_acceleration']}")
    dashboard_logger.info(f"   - Processing Strategy: {processing_strategy}")
    dashboard_logger.info(f"   - Batch Size: {batch_size}")
    
    orchestrator = None 
    high_volume_orchestrator = None
    
    try:
        # Initialize appropriate orchestrator based on GPU availability and user selection
        if system_caps["nvidia_acceleration"] and processing_strategy in ["Auto-Select", "GPU-Accelerated", "Hybrid (GPU+CPU)"]:
            # Initialize High-Volume Orchestrator for GPU acceleration
            dashboard_logger.info("Initializing High-Volume Orchestrator with GPU acceleration...")
            from continuum5g_insight.agents.anomaly_detection_agent import MODEL_PATH_BINARY, MODEL_PATH_MULTICLASS, MODEL_PATH_BINARY_SCALER, MODEL_PATH_MULTI_SCALER
            
            high_volume_orchestrator = HighVolumeProcessingOrchestrator(
                binary_model_path=MODEL_PATH_BINARY,
                multiclass_model_path=MODEL_PATH_MULTICLASS,
                binary_scaler_path=MODEL_PATH_BINARY_SCALER,
                multi_scaler_path=MODEL_PATH_MULTI_SCALER
            )
            orchestrator = OrchestrationAgent()  # Still need the full orchestrator for threat analysis and response
            # Use the high-volume detection agent for anomaly detection
            orchestrator.anomaly_detector = high_volume_orchestrator.detection_agent
        elif batch_size > 1 and processing_strategy in ["Auto-Select", "CPU-Only"]:
            # Initialize High-Volume Orchestrator for CPU batch processing
            dashboard_logger.info("Initializing High-Volume Orchestrator for CPU batch processing...")
            from continuum5g_insight.agents.anomaly_detection_agent import MODEL_PATH_BINARY, MODEL_PATH_MULTICLASS, MODEL_PATH_BINARY_SCALER, MODEL_PATH_MULTI_SCALER
            
            high_volume_orchestrator = HighVolumeProcessingOrchestrator(
                binary_model_path=MODEL_PATH_BINARY,
                multiclass_model_path=MODEL_PATH_MULTICLASS,
                binary_scaler_path=MODEL_PATH_BINARY_SCALER,
                multi_scaler_path=MODEL_PATH_MULTI_SCALER
            )
            orchestrator = OrchestrationAgent()  # Still need the full orchestrator for threat analysis and response
            # Use the high-volume detection agent for anomaly detection
            orchestrator.anomaly_detector = high_volume_orchestrator.detection_agent
        else:
            # Use standard orchestrator for single-record processing
            dashboard_logger.info("Initializing standard OrchestrationAgent...")
            orchestrator = OrchestrationAgent()
        
        # Model loading checks (from original code)
        if orchestrator.anomaly_detector.binary_model is None or \
           orchestrator.anomaly_detector.multiclass_model is None or \
           (orchestrator.anomaly_detector.scaler_binary is None and orchestrator.anomaly_detector.scaler_multi is None): # Adjusted logic for scalers
            err_msg = "CRITICAL ERROR: Anomaly detection models or scaler failed to load. Cannot proceed."
            dashboard_logger.error(err_msg)
            st.error(err_msg + " Check paths in `anomaly_detection_agent.py` and files in `models/rf_models/`.")
            st.stop()
        # ... (KB warnings if needed) ...
    except Exception as e:
        dashboard_logger.error(f"Error initializing OrchestrationAgent: {e}", exc_info=True)
        st.error(f"Error initializing OrchestrationAgent: {e}")
        st.exception(e) 
        st.stop()
    
    dashboard_logger.info("Orchestrator initialized successfully.")

    df_to_stream = None
    data_source_info = ""
    # Data loading logic from uploaded_file or default_csv_path
    if uploaded_file is not None:
        try:
            df_to_stream = pd.read_csv(uploaded_file)
            data_source_info = f"Processing from uploaded file: {uploaded_file.name}"
            dashboard_logger.info(f"Using uploaded file: {uploaded_file.name}")
            if df_to_stream.empty:
                dashboard_logger.warning("Uploaded CSV file is empty.")
                st.warning("Uploaded CSV file is empty.")
                st.stop()
        except Exception as e:
            dashboard_logger.error(f"Error reading uploaded CSV: {e}", exc_info=True)
            st.error(f"Error reading uploaded CSV: {e}")
            st.exception(e)
            st.stop()
    else:
        actual_default_path = default_csv_path.replace(".placeholder", "")
        if os.path.exists(actual_default_path):
            df_to_stream = pd.read_csv(actual_default_path)
            data_source_info = f"Processing from default CSV: {actual_default_path}"
            dashboard_logger.info(f"Using default file: {actual_default_path}")
        elif os.path.exists(default_csv_path): # Using placeholder
            df_to_stream = pd.read_csv(default_csv_path) # This might fail if it's truly just a placeholder
            data_source_info = f"Processing from placeholder CSV: {default_csv_path}"
            st.warning("Using placeholder dataset. For meaningful results, replace the placeholder.")
            dashboard_logger.warning(f"Using placeholder file: {default_csv_path}")
        else:
            err_msg = f"Default dataset not found at '{actual_default_path}' or placeholder '{default_csv_path}'. Please upload a CSV."
            dashboard_logger.error(err_msg)
            st.error(err_msg)
            st.stop()
        if df_to_stream.empty:
            st.error("Loaded CSV file is empty.")
            st.stop()
        # Check for 'labelmalicious' column
        if "labelmalicious" in df_to_stream.columns:
            st.session_state.actual_malicious_present_in_csv = True
            dashboard_logger.info("'labelmalicious' column found in the dataset.")
        else:
            st.session_state.actual_malicious_present_in_csv = False
            dashboard_logger.info("'labelmalicious' column NOT found in the dataset.")
            actual_malicious_sidebar_placeholder.empty()

    streamer_instance = None
    try:
        streamer_instance = DataStreamer(source_dataframe=df_to_stream)
        if streamer_instance.df is None or streamer_instance.df.empty:
            dashboard_logger.error("DataFrame is empty or could not be loaded by DataStreamer.")
            st.error("DataFrame is empty or could not be loaded by DataStreamer.")
            st.stop()
    except Exception as e:
        # ... (error handling for DataStreamer init) ...
        dashboard_logger.error(f"Error initializing DataStreamer: {e}", exc_info=True)
        st.error(f"Error initializing DataStreamer: {e}")
        st.exception(e)
        st.stop()

    # Determine number of records and delay based on mode
    records_to_process_this_run = 0
    inter_record_delay = 0.0

    if st.session_state.requests_per_second > 0:
        inter_record_delay = 1.0 / st.session_state.requests_per_second
    else:
        inter_record_delay = 0.01 # Minimal delay if RPS is zero or invalid

    if st.session_state.processing_mode == "Continuous Stream (Simulated)":
        records_to_process_this_run = st.session_state.max_records_continuous
        st.session_state.run_active = True 
    else: # Manual Batch
        records_to_process_this_run = st.session_state.max_records_manual
        st.session_state.run_active = True # Also set for manual batch to allow potential stop

    # processed_count = 0 # Replaced by st.session_state.processed_requests_current_run
    try:
        dashboard_logger.info(f"Starting processing loop for max {records_to_process_this_run} records. Mode: {st.session_state.processing_mode}, RPS: {st.session_state.requests_per_second}")
        dashboard_logger.info(f"🔧 Processing Configuration:")
        dashboard_logger.info(f"   - High Volume Orchestrator: {'✅ Active' if high_volume_orchestrator else '❌ None'}")
        dashboard_logger.info(f"   - Batch Size: {batch_size}")
        dashboard_logger.info(f"   - Will use batch processing: {'✅ Yes' if (high_volume_orchestrator and batch_size > 1) else '❌ No'}")
        
        # Get the actual feature columns used by the model from the orchestrator
        # This is crucial for displaying the correct part of the input record if it's very wide.
        # Assuming anomaly_detector has an attribute like `feature_columns`
        feature_cols = []
        if hasattr(orchestrator.anomaly_detector, 'feature_columns') and orchestrator.anomaly_detector.feature_columns:
            feature_cols = orchestrator.anomaly_detector.feature_columns
            dashboard_logger.info(f"Using feature columns for display: {feature_cols}")


        # Collect records into batches for GPU processing if using high-volume orchestrator
        if high_volume_orchestrator and batch_size > 1:
            # Process in batches for optimal GPU utilization
            dashboard_logger.info(f"🔄 BATCH PROCESSING MODE: batch_size={batch_size}, orchestrator_type=HighVolume")
            st.sidebar.info(f"🔄 Batch Processing: {batch_size} records per batch")
            
            batch_records = []
            batch_count = 0
            batch_start_time = time.time()
            
            for record_dict_full_row in streamer_instance.send_n_records(n_records=records_to_process_this_run):
                if not st.session_state.run_active:
                    st.sidebar.info("Processing stopped.")
                    break
                
                batch_records.append(record_dict_full_row)
                
                # Process batch when full or at end of stream
                if len(batch_records) >= batch_size or st.session_state.processed_requests_current_run + len(batch_records) >= records_to_process_this_run:
                    batch_count += 1
                    dashboard_logger.info(f"📦 Processing batch {batch_count} with {len(batch_records)} records")
                    st.sidebar.text(f"📦 Processing batch {batch_count} ({len(batch_records)} records)")
                    
                    # Process batch with GPU acceleration
                    batch_start = time.time()
                    batch_results = high_volume_orchestrator.process_batch(batch_records)
                    batch_end = time.time()
                    
                    # Record performance metrics
                    batch_processing_time = batch_end - batch_start
                    throughput = len(batch_records) / batch_processing_time if batch_processing_time > 0 else 0
                    
                    dashboard_logger.info(f"✅ Batch {batch_count} completed in {batch_processing_time:.3f}s, throughput: {throughput:.1f} records/sec")
                    
                    st.session_state.gpu_performance_stats["batch_processing_time"].append(batch_processing_time)
                    st.session_state.gpu_performance_stats["throughput_records_per_sec"].append(throughput)
                    
                    # Update device stats based on processing strategy
                    if processing_strategy == "GPU-Accelerated":
                        st.session_state.processing_device_stats["GPU"] += len(batch_records)
                    elif processing_strategy == "Hybrid (GPU+CPU)":
                        st.session_state.processing_device_stats["Hybrid"] += len(batch_records)
                    else:
                        st.session_state.processing_device_stats["CPU"] += len(batch_records)
                    
                    # Process each result in the batch
                    for i, (record_dict_full_row, batch_result) in enumerate(zip(batch_records, batch_results)):
                        st.session_state.processed_requests_current_run += 1
                        st.session_state.total_requests_count += 1
                        current_timestamp = datetime.datetime.now()
                        
                        # Update current throughput
                        update_current_throughput()
                        
                        # Display only the last record in the batch for UI responsiveness
                        if i == len(batch_records) - 1:
                            input_display_data = record_dict_full_row
                            if feature_cols and len(record_dict_full_row.keys()) > len(feature_cols) + 5:
                                input_display_data = {k: record_dict_full_row[k] for k in feature_cols if k in record_dict_full_row}
                                input_display_data["...other_fields..."] = "(omitted for brevity)"
                            current_input_placeholder.json({"Current Input Record (Key Features or Full)": input_display_data})
                        
                        # Process the detection result - handle both nested and direct formats
                        if 'detection_result' in batch_result:
                            # Nested format from high-volume orchestrator
                            detection_alert = batch_result.get('detection_result', {})
                            performance_metrics = batch_result.get('performance_metrics', {})
                        else:
                            # Direct format (fallback compatibility)
                            detection_alert = batch_result
                            performance_metrics = {}
                        
                        # Update GPU performance metrics
                        if performance_metrics:
                            if 'preprocessing_time' in performance_metrics:
                                st.session_state.gpu_performance_stats["preprocessing_time"].append(performance_metrics['preprocessing_time'])
                            if 'inference_time' in performance_metrics:
                                st.session_state.gpu_performance_stats["inference_time"].append(performance_metrics['inference_time'])
                            if 'gpu_memory_usage' in performance_metrics:
                                st.session_state.gpu_performance_stats["gpu_memory_usage"].append(performance_metrics['gpu_memory_usage'])
                        
                        # Process detection alert (existing logic)
                        process_detection_result(record_dict_full_row, detection_alert, current_timestamp, orchestrator)
                    
                    batch_records = []  # Reset batch
                    update_display(pd.DataFrame())  # Update display after batch
                    time.sleep(inter_record_delay)
        else:
            # Single record processing (original logic)
            dashboard_logger.info(f"🔄 SINGLE RECORD PROCESSING MODE: orchestrator_type=Standard")
            st.sidebar.info("🔄 Single Record Processing")
            
            for record_dict_full_row in streamer_instance.send_n_records(n_records=records_to_process_this_run):
                if not st.session_state.run_active:
                    st.sidebar.info("Processing stopped.")
                    break
                
                st.session_state.processed_requests_current_run += 1
                st.session_state.total_requests_count += 1
                current_timestamp = datetime.datetime.now()

                # Update current throughput
                update_current_throughput()

                # Increment actual malicious count if applicable
                if st.session_state.actual_malicious_present_in_csv:
                    if record_dict_full_row.get('labelmalicious') == 1:
                        st.session_state.actual_malicious_count_current_run += 1

                # Display current input
                input_display_data = record_dict_full_row
                if feature_cols and len(record_dict_full_row.keys()) > len(feature_cols) + 5:
                    input_display_data = {k: record_dict_full_row[k] for k in feature_cols if k in record_dict_full_row}
                    input_display_data["...other_fields..."] = "(omitted for brevity)"
                current_input_placeholder.json({"Current Input Record (Key Features or Full)": input_display_data})
                
                # Record CPU processing
                st.session_state.processing_device_stats["CPU"] += 1
                 # Model Prediction with 0.3 threshold
                detection_alert = orchestrator.anomaly_detector.detect_anomalies(record_dict_full_row)
                
                # Process the detection result using helper function
                process_detection_result(record_dict_full_row, detection_alert, current_timestamp, orchestrator)
                
                update_display(pd.DataFrame())
                time.sleep(inter_record_delay)
        
        st.sidebar.success(f"Finished processing {st.session_state.processed_requests_current_run} requests.")
        dashboard_logger.info(f"Finished processing {st.session_state.processed_requests_current_run} requests.")
        if st.session_state.processed_requests_current_run == 0:
            st.sidebar.warning("No records were processed in this run.")
        st.session_state.run_active = False

    except Exception as e:
        dashboard_logger.error(f"An error occurred during stream processing: {e}", exc_info=True)
        st.error(f"An error occurred during stream processing: {e}")
        st.exception(e)
        st.session_state.run_active = False

# Final update after loop finishes or if run_button was not pressed (initial load)
# Ensure df_to_stream is available or pass an empty DataFrame if it's not in this scope
# For safety, we can re-check the CSV status if df_to_stream is not directly available here
# However, update_display is called within the loop with df_to_stream.
# The call outside the loop might need a way to access the column info if it's cleared.
# For now, assuming the last df_to_stream context is sufficient or an empty df is fine for non-run updates.
update_display(df_to_stream if 'df_to_stream' in locals() and df_to_stream is not None else pd.DataFrame())

if __name__ == '__main__':
    # This message is for running the script directly, Streamlit handles it.
    # dashboard_logger.info("To view the dashboard, run: `streamlit run dashboard.py` in your terminal from the `continuum5g_insight_project` directory.")
    pass
