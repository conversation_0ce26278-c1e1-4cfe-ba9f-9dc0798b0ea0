#!/usr/bin/env python3
"""
Test script to verify batch processing functionality
"""

import os
import sys
import logging
import pandas as pd

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

try:
    from continuum5g_insight.agents.high_volume_orchestrator import HighVolumeProcessingOrchestrator
    from continuum5g_insight.agents.data_streaming_agent import DataStreamer
    from continuum5g_insight.agents.anomaly_detection_agent import MODEL_PATH_BINARY, MODEL_PATH_MULTICLASS, MODEL_PATH_BINARY_SCALER, MODEL_PATH_MULTI_SCALER
    from continuum5g_insight.core.utils import get_gpu_device_info, is_cudf_available
    
    logger.info("✅ All imports successful!")
    
    # Test GPU detection
    gpu_info = get_gpu_device_info()
    cudf_available = is_cudf_available()
    
    logger.info(f"🔧 System Info:")
    logger.info(f"   - GPU Available: {gpu_info['gpu_available']}")
    logger.info(f"   - GPU Count: {gpu_info['device_count']}")
    logger.info(f"   - cuDF Available: {cudf_available}")
    
    # Test High-Volume Orchestrator initialization
    logger.info("🚀 Initializing High-Volume Orchestrator...")
    orchestrator = HighVolumeProcessingOrchestrator(
        binary_model_path=MODEL_PATH_BINARY,
        multiclass_model_path=MODEL_PATH_MULTICLASS,
        binary_scaler_path=MODEL_PATH_BINARY_SCALER,
        multi_scaler_path=MODEL_PATH_MULTI_SCALER
    )
    logger.info("✅ High-Volume Orchestrator initialized!")
    
    # Test DataStreamer
    dataset_path = "data/sample_inputs/5g_nidd_dataset.csv"
    if os.path.exists(dataset_path):
        logger.info(f"📊 Loading test data from {dataset_path}")
        streamer = DataStreamer(source_dataframe=pd.read_csv(dataset_path))
        
        # Test batch processing
        batch_size = 5
        logger.info(f"🔄 Testing batch processing with batch_size={batch_size}")
        
        batch_records = []
        record_count = 0
        max_records = 10
        
        for record in streamer.send_n_records(n_records=max_records):
            batch_records.append(record)
            record_count += 1
            
            if len(batch_records) >= batch_size or record_count >= max_records:
                logger.info(f"📦 Processing batch with {len(batch_records)} records")
                
                # Process batch
                batch_results = orchestrator.process_batch(batch_records)
                
                logger.info(f"✅ Batch processed successfully!")
                logger.info(f"   - Records in: {len(batch_records)}")
                logger.info(f"   - Results out: {len(batch_results)}")
                
                # Show sample result
                if batch_results:
                    sample_result = batch_results[0]
                    logger.info(f"   - Sample result keys: {list(sample_result.keys())}")
                
                batch_records = []  # Reset batch
                
                if record_count >= max_records:
                    break
        
        logger.info("🎉 Batch processing test completed successfully!")
    else:
        logger.error(f"❌ Dataset not found: {dataset_path}")
    
except Exception as e:
    logger.error(f"❌ Test failed: {e}", exc_info=True)
