# Continuum5G Insight - Core Utilities
# This file will contain utility functions used across the Continuum5G Insight system.

import subprocess
import logging

logger = logging.getLogger(__name__)

def is_nvidia_gpu_available():
    """
    Checks if an NVIDIA GPU is available and nvidia-smi is accessible.
    Returns:
        bool: True if an NVIDIA GPU is detected, False otherwise.
    """
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, check=False)
        if result.returncode == 0:
            logger.info("NVIDIA GPU detected via nvidia-smi.")
            return True
        else:
            logger.warning(f"nvidia-smi command failed with return code {result.returncode}. Output: {result.stderr.strip()}")
            logger.warning("NVIDIA GPU not detected or nvidia-smi not functioning correctly.")
            return False
    except FileNotFoundError:
        logger.warning("nvidia-smi command not found. NVIDIA GPU status cannot be determined.")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while checking for NVIDIA GPU: {e}", exc_info=True)
        return False

def is_cuml_available():
    """
    Checks if the RAPIDS cuML library is installed and importable.
    Returns:
        bool: True if cuML is available, False otherwise.
    """
    try:
        import cuml
        logger.info("RAPIDS cuML library is available.")
        return True
    except ImportError:
        logger.warning("RAPIDS cuML library not found. GPU acceleration for scikit-learn like models via cuML is not available.")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while checking for cuML: {e}", exc_info=True)
        return False

def is_cudf_available():
    """
    Checks if the RAPIDS cuDF library is installed and importable.
    Returns:
        bool: True if cuDF is available, False otherwise.
    """
    try:
        import cudf
        logger.info("RAPIDS cuDF library is available.")
        return True
    except ImportError:
        logger.warning("RAPIDS cuDF library not found. GPU acceleration for dataframe operations not available.")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred while checking for cuDF: {e}", exc_info=True)
        return False

def get_gpu_device_info():
    """
    Get detailed GPU device information using nvidia-ml-py if available.
    Returns:
        dict: GPU information including memory, device count, etc.
    """
    gpu_info = {
        "gpu_available": False,
        "device_count": 0,
        "total_memory_mb": 0,
        "free_memory_mb": 0,
        "driver_version": "Unknown",
        "devices": []
    }
    
    try:
        import pynvml
        pynvml.nvmlInit()
        
        device_count = pynvml.nvmlDeviceGetCount()
        gpu_info["device_count"] = device_count
        gpu_info["gpu_available"] = device_count > 0
        
        # Get driver version
        try:
            gpu_info["driver_version"] = pynvml.nvmlSystemGetDriverVersion().decode('utf-8')
        except:
            pass
        
        # Get device information
        for i in range(device_count):
            handle = pynvml.nvmlDeviceGetHandleByIndex(i)
            name = pynvml.nvmlDeviceGetName(handle).decode('utf-8')
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            
            device_info = {
                "index": i,
                "name": name,
                "total_memory_mb": mem_info.total // (1024 * 1024),
                "free_memory_mb": mem_info.free // (1024 * 1024),
                "used_memory_mb": mem_info.used // (1024 * 1024)
            }
            gpu_info["devices"].append(device_info)
            
            if i == 0:  # Use first device for general stats
                gpu_info["total_memory_mb"] = device_info["total_memory_mb"]
                gpu_info["free_memory_mb"] = device_info["free_memory_mb"]
        
        logger.info(f"Detected {device_count} GPU device(s) with total memory: {gpu_info['total_memory_mb']} MB")
        
    except ImportError:
        logger.warning("pynvml not available. Using basic nvidia-smi check only.")
        gpu_info["gpu_available"] = is_nvidia_gpu_available()
    except Exception as e:
        logger.error(f"Error getting GPU device info: {e}")
        gpu_info["gpu_available"] = is_nvidia_gpu_available()
    
    return gpu_info

def select_optimal_device():
    """
    Select the optimal device (CPU/GPU) for processing based on availability.
    Returns:
        tuple: (device_type, device_info) where device_type is 'cpu' or 'gpu'
    """
    gpu_info = get_gpu_device_info()
    
    if gpu_info["gpu_available"] and gpu_info["free_memory_mb"] > 1000:  # Require at least 1GB free
        cuml_available = is_cuml_available()
        cudf_available = is_cudf_available()
        
        if cuml_available or cudf_available:
            logger.info(f"GPU device selected for processing. Available libraries: cuML={cuml_available}, cuDF={cudf_available}")
            return "gpu", {
                "cuml_available": cuml_available,
                "cudf_available": cudf_available,
                "gpu_info": gpu_info
            }
    
    logger.info("CPU device selected for processing.")
    return "cpu", {"gpu_info": gpu_info}

if __name__ == '__main__':
    # Configure basic logging for direct script testing
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    print("=== NVIDIA Technology Capabilities Check ===")
    gpu_present = is_nvidia_gpu_available()
    print(f"NVIDIA GPU Available: {gpu_present}")
    
    cuml_available = is_cuml_available()
    print(f"RAPIDS cuML Available: {cuml_available}")
    
    cudf_available = is_cudf_available()
    print(f"RAPIDS cuDF Available: {cudf_available}")
    
    gpu_info = get_gpu_device_info()
    print(f"GPU Device Info: {gpu_info}")
    
    device_type, device_info = select_optimal_device()
    print(f"Optimal Device: {device_type}")
    print(f"Device Info: {device_info}")
    
    print("=== End NVIDIA Technology Check ===")
    
    # Test cuML availability for RandomForest if available
    if cuml_available and gpu_present:
        try:
            import cuml
            from cuml.ensemble import RandomForestClassifier as cuRF
            print("cuML RandomForest classifier is available for GPU acceleration")
        except Exception as e:
            print(f"cuML RandomForest not accessible: {e}")
