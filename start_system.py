#!/usr/bin/env python3
"""
Quick Start Script for 5G Security Analytics Platform
Launches both control panel and dashboard
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def main():
    """Launch both interfaces"""
    print("🛡️ Starting 5G Security Analytics Platform")
    print("=" * 50)
    
    # Check if required files exist
    if not Path("data/sample_inputs/5g_nidd_dataset.csv").exists():
        print("❌ Dataset not found: data/sample_inputs/5g_nidd_dataset.csv")
        sys.exit(1)
    
    if not Path("models/rf_models").exists():
        print("❌ Models directory not found: models/rf_models")
        sys.exit(1)
    
    print("✅ Prerequisites check passed")
    print()
    
    try:
        # Start Gradio control panel
        print("🎛️ Starting Gradio Control Panel...")
        control_process = subprocess.Popen([
            sys.executable, "app.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a bit for Gradio to start
        time.sleep(3)
        
        # Start Streamlit dashboard
        print("📊 Starting Streamlit Dashboard...")
        dashboard_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "dashboard.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for both to start
        time.sleep(5)
        
        print("✅ Both interfaces started successfully!")
        print()
        print("🌐 Access URLs:")
        print("   • Control Panel (Gradio):  http://localhost:7860")
        print("   • Dashboard (Streamlit):   http://localhost:8501")
        print()
        print("📋 Usage Instructions:")
        print("   1. Use Control Panel to configure and start processing")
        print("   2. Monitor real-time results in Dashboard")
        print("   3. Press Ctrl+C to stop both services")
        print()
        
        # Open browsers
        print("🌐 Opening browsers...")
        webbrowser.open("http://localhost:7860")
        time.sleep(1)
        webbrowser.open("http://localhost:8501")
        
        # Wait for user interrupt
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping services...")
            control_process.terminate()
            dashboard_process.terminate()
            
            # Wait for processes to terminate
            control_process.wait(timeout=5)
            dashboard_process.wait(timeout=5)
            
            print("✅ Services stopped successfully")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
