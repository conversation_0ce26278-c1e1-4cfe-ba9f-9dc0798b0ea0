# Continuum5G Insight - Data Streaming Agent
# Responsible for simulating or ingesting data streams, e.g., from the 5G-NIDD dataset.

import pandas as pd
import time
import os
import logging

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

class DataStreamer:
    """
    A class to simulate or manage data streaming from a pandas DataFrame.
    It can load data from a CSV or use an existing DataFrame.
    Offers controllable streaming options like sending N records or continuous streaming
    with adjustable frequency, start, stop, and reset capabilities.
    """
    def __init__(self, source_dataframe=None, csv_path=None, default_frequency_hz=10):
        """
        Initializes the DataStreamer.

        Args:
            source_dataframe (pd.DataFrame, optional): A pandas DataFrame to use as the data source.
            csv_path (str, optional): Path to a CSV file to load as the data source.
                                      Used if source_dataframe is None.
            default_frequency_hz (int, optional): Default streaming frequency in records per second.
        """
        self.df = None
        self.is_streaming = False
        self.current_index = 0
        logger.info("Initializing DataStreamer...")
        
        if source_dataframe is not None and isinstance(source_dataframe, pd.DataFrame):
            self.df = source_dataframe
            logger.info(f"DataStreamer initialized with provided DataFrame. Shape: {self.df.shape}")
        elif csv_path:
            self.load_dataframe_from_csv(csv_path)
        else:
            logger.warning("DataStreamer initialized without a data source (no DataFrame or CSV path provided).")
            self.df = pd.DataFrame() # Initialize with an empty DataFrame

        self.set_frequency(default_frequency_hz) 
        logger.info("DataStreamer initialization complete.")

    def load_dataframe_from_csv(self, csv_path):
        """
        Loads data from a CSV file into the streamer's DataFrame.
        Handles placeholder path resolution for .placeholder files.
        The provided csv_path can be absolute or relative to the caller.
        """
        logger.info(f"DataStreamer: Attempting to load dataset from provided path: {csv_path}")
        
        print("------------------------------------------------------------------------------------")
        print("DataStreamer IMPORTANT REMINDER (CSV Loading):")
        print("If using a placeholder path (e.g., ending with '.placeholder'),")
        print("ensure the actual data file (without '.placeholder') exists in the same location,")
        print("or update the path to your actual 5G-NIDD dataset.")
        print(f"Path provided to load_dataframe_from_csv: {csv_path}")
        print(f"Attempting to resolve to absolute path: {os.path.abspath(csv_path)}")
        print("------------------------------------------------------------------------------------")

        actual_csv_path_to_load = csv_path
        try:
            if not os.path.isabs(actual_csv_path_to_load):
                 actual_csv_path_to_load = os.path.abspath(actual_csv_path_to_load)
                 logger.debug(f"DataStreamer: Resolved relative path to absolute: {actual_csv_path_to_load}")


            if actual_csv_path_to_load.endswith(".placeholder"):
                resolved_path_no_placeholder = actual_csv_path_to_load.replace(".placeholder", "")
                if not os.path.exists(resolved_path_no_placeholder):
                    logger.warning(f"DataStreamer: Placeholder path is configured ('{actual_csv_path_to_load}'). Actual data file ('{resolved_path_no_placeholder}') not found. Attempting to load placeholder file itself.")
                    if not os.path.exists(actual_csv_path_to_load): 
                         raise FileNotFoundError(f"The placeholder file '{actual_csv_path_to_load}' itself was not found.")
                else:
                    logger.info(f"DataStreamer: Placeholder path is configured but actual data file ('{resolved_path_no_placeholder}') found. Using actual data file.")
                    actual_csv_path_to_load = resolved_path_no_placeholder
            
            self.df = pd.read_csv(actual_csv_path_to_load)
            
            if self.df.empty:
                logger.warning(f"DataStreamer: The dataset at '{actual_csv_path_to_load}' is empty. Streamer will have no data.")
            else:
                logger.info(f"DataStreamer: Successfully loaded dataset into DataStreamer from: {actual_csv_path_to_load}. Shape: {self.df.shape}")

        except FileNotFoundError:
            logger.error(f"DataStreamer: Dataset file not found at {actual_csv_path_to_load}.")
            self.df = pd.DataFrame() 
        except pd.errors.EmptyDataError:
            logger.error(f"DataStreamer: The dataset file at {actual_csv_path_to_load} is empty or not a valid CSV.")
            self.df = pd.DataFrame()
        except Exception as e:
            logger.error(f"DataStreamer: An unexpected error occurred during CSV loading: {e}", exc_info=True)
            self.df = pd.DataFrame()
        
        self.reset_stream() 

    def set_frequency(self, records_per_second):
        if records_per_second <= 0:
            logger.error("Stream frequency must be greater than 0. Using default of 1 Hz.")
            self.stream_frequency_hz = 1
        else:
            self.stream_frequency_hz = records_per_second
        
        self.delay_seconds = 1.0 / self.stream_frequency_hz
        logger.info(f"Stream frequency set to {self.stream_frequency_hz} Hz (delay: {self.delay_seconds:.4f}s).")

    def start_stream(self):
        if self.df is None or self.df.empty:
            logger.error("Cannot start stream. DataFrame is not loaded or is empty.")
            return
        self.is_streaming = True
        logger.info("Stream started/resumed.")

    def stop_stream(self):
        self.is_streaming = False
        logger.info("Stream stopped.")

    def reset_stream(self):
        self.current_index = 0
        self.is_streaming = False 
        logger.info("Stream reset to beginning and stopped.")

    def send_n_records(self, n_records):
        if self.df is None or self.df.empty:
            logger.error("DataFrame not loaded or is empty. Cannot send records.")
            return 

        logger.info(f"Attempting to send {n_records} records...")
        records_yielded = 0
        for _ in range(n_records):
            if self.current_index >= len(self.df):
                logger.info("End of DataFrame reached while sending records.")
                break
            
            record = self.df.iloc[self.current_index].to_dict()
            self.current_index += 1
            records_yielded += 1
            yield record
        logger.info(f"Finished sending {records_yielded} of {n_records} requested records. Current index: {self.current_index}")

    def continuous_stream(self):
        if self.df is None or self.df.empty:
            logger.error("DataFrame not loaded or is empty. Cannot start continuous stream.")
            return 

        if not self.is_streaming:
            logger.warning("continuous_stream() called without explicit start_stream(). Starting stream now.")
            self.start_stream()

        logger.info("Starting continuous stream...")
        while self.is_streaming:
            if self.current_index >= len(self.df):
                logger.info("End of DataFrame reached during continuous stream. Resetting to beginning.")
                self.current_index = 0
                if self.df.empty: 
                    logger.error("DataFrame is empty after reset. Halting continuous stream.")
                    self.stop_stream() 
                    break
            
            record = self.df.iloc[self.current_index].to_dict()
            self.current_index += 1
            yield record
            
            time.sleep(self.delay_seconds)
        
        if not self.is_streaming:
             logger.info("Continuous stream halted by stop_stream() or internal error (e.g. empty df).")


if __name__ == '__main__':
    # Basic logging setup for direct testing of this script
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', stream=sys.stdout)
    logger.info("--- Test DataStreamer Class (Direct Run) ---")

    TEST_CSV_PATH = "../../data/sample_inputs/5g_nidd_dataset.csv.placeholder" 
    abs_test_csv_path = os.path.abspath(os.path.join(os.path.dirname(__file__), TEST_CSV_PATH))

    logger.info(f"Reminder: This test uses the placeholder CSV from its resolved path: '{abs_test_csv_path}'.")

    logger.info("\n--- Test 1: Initialize with CSV Path ---")
    streamer_from_csv = DataStreamer(csv_path=abs_test_csv_path, default_frequency_hz=5)
    if streamer_from_csv.df is None or streamer_from_csv.df.empty:
        logger.error("Test 1 SKIPPED: Could not load DataFrame from CSV for further tests.")
    else:
        logger.info("\n--- Test 2: Send N Records (N=3) ---")
        records_sent_count = 0
        for record in streamer_from_csv.send_n_records(3):
            logger.debug(f"  Sent record: {record}")
            records_sent_count += 1
        logger.info(f"  Actually sent {records_sent_count} records.")

        logger.info("\n--- Test 3: Continuous Stream (approx 3 seconds, then change frequency, then stop) ---")
        streamer_from_csv.reset_stream() 
        streamer_from_csv.start_stream()
        
        logger.info("  Starting continuous stream at 5 Hz for ~1.5 seconds...")
        stream_count = 0
        start_time = time.time()
        for record in streamer_from_csv.continuous_stream():
            stream_count += 1
            logger.debug(f"  Streamed continuously ({stream_count}): {record}")
            if time.time() - start_time > 1.5: 
                break
        
        logger.info("\n  Changing frequency to 1 Hz mid-stream...")
        streamer_from_csv.set_frequency(1) 
        
        logger.info("  Continuing stream at 1 Hz for ~2 seconds...")
        start_time_slow = time.time()
        for record in streamer_from_csv.continuous_stream(): 
            if not streamer_from_csv.is_streaming: 
                break
            stream_count += 1
            logger.debug(f"  Streamed continuously ({stream_count}) at 1Hz: {record}")
            if time.time() - start_time_slow > 2: 
                break

        streamer_from_csv.stop_stream()
        logger.info(f"  Total records streamed continuously: {stream_count}")

        logger.info("\n--- Test 4: Reset Stream ---")
        streamer_from_csv.reset_stream()
        logger.info(f"  Streamer current index after reset: {streamer_from_csv.current_index}")
        logger.info(f"  Streamer is_streaming after reset: {streamer_from_csv.is_streaming}")
        logger.info("  Sending 1 record after reset:")
        records_after_reset_count = 0
        for record in streamer_from_csv.send_n_records(1):
            logger.debug(f"  Sent after reset: {record}")
            records_after_reset_count +=1
        if records_after_reset_count == 0 and not (streamer_from_csv.df is None or streamer_from_csv.df.empty):
            logger.warning("  Could not send record after reset, check DataFrame status.")

    logger.info("\n--- Test 5: Initialize with Direct DataFrame ---")
    sample_data = {'featureA': [10, 20, 30, 40, 50], 'featureB': ['x', 'y', 'z', 'xx', 'yy'], 'label': [0,1,0,1,0]}
    sample_df = pd.DataFrame(sample_data)
    streamer_direct_df = DataStreamer(source_dataframe=sample_df, default_frequency_hz=20)
    
    logger.info("  Sending 2 records from direct DataFrame streamer:")
    direct_df_sent_count = 0
    for record in streamer_direct_df.send_n_records(2):
        logger.debug(f"  Sent from direct DF: {record}")
        direct_df_sent_count +=1
    logger.info(f"  Sent {direct_df_sent_count} records from direct DataFrame.")

    logger.info("\n--- Finished DataStreamer Class Test (Direct Run) ---")
    
