"""
Batch Processing Module
Handles batch processing of 5G network data for anomaly detection
"""

import time
import logging
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
from tqdm import tqdm

from ..models.anomaly_detector import AnomalyDetector

logger = logging.getLogger(__name__)


class BatchProcessor:
    """
    Handles batch processing of network data for anomaly detection
    """
    
    def __init__(self, detector: AnomalyDetector, max_workers: int = 4):
        """
        Initialize batch processor
        
        Args:
            detector: AnomalyDetector instance
            max_workers: Maximum number of worker threads
        """
        self.detector = detector
        self.max_workers = max_workers
        self.processing_stats = {
            "total_batches": 0,
            "total_records": 0,
            "total_anomalies": 0,
            "total_processing_time": 0.0,
            "avg_batch_time": 0.0,
            "throughput_records_per_sec": 0.0
        }
    
    def process_single_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single record
        
        Args:
            record: Input data record
            
        Returns:
            Detection result dictionary
        """
        return self.detector.detect_anomaly(record)
    
    def process_batch_sequential(self, batch_data: List[Dict[str, Any]], 
                                progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        Process a batch of records sequentially
        
        Args:
            batch_data: List of input records
            progress_callback: Optional callback function for progress updates
            
        Returns:
            List of detection results
        """
        results = []
        start_time = time.time()
        
        for i, record in enumerate(batch_data):
            result = self.process_single_record(record)
            results.append(result)
            
            if progress_callback:
                progress_callback(i + 1, len(batch_data))
        
        # Update stats
        processing_time = time.time() - start_time
        self._update_stats(len(batch_data), results, processing_time)
        
        return results
    
    def process_batch_parallel(self, batch_data: List[Dict[str, Any]], 
                              progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        Process a batch of records in parallel
        
        Args:
            batch_data: List of input records
            progress_callback: Optional callback function for progress updates
            
        Returns:
            List of detection results
        """
        results = [None] * len(batch_data)
        start_time = time.time()
        completed_count = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_index = {
                executor.submit(self.process_single_record, record): i 
                for i, record in enumerate(batch_data)
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    completed_count += 1
                    
                    if progress_callback:
                        progress_callback(completed_count, len(batch_data))
                        
                except Exception as e:
                    logger.error(f"Error processing record {index}: {e}")
                    results[index] = {"error": str(e)}
        
        # Update stats
        processing_time = time.time() - start_time
        self._update_stats(len(batch_data), results, processing_time)
        
        return results
    
    def process_dataframe(self, df: pd.DataFrame, batch_size: int = 100, 
                         parallel: bool = True, progress_callback: Optional[Callable] = None) -> pd.DataFrame:
        """
        Process an entire DataFrame in batches
        
        Args:
            df: Input DataFrame
            batch_size: Size of each batch
            parallel: Whether to use parallel processing
            progress_callback: Optional callback for progress updates
            
        Returns:
            DataFrame with detection results
        """
        all_results = []
        total_batches = (len(df) + batch_size - 1) // batch_size
        
        logger.info(f"Processing {len(df)} records in {total_batches} batches of size {batch_size}")
        
        for batch_idx in range(0, len(df), batch_size):
            batch_df = df.iloc[batch_idx:batch_idx + batch_size]
            batch_data = batch_df.to_dict('records')
            
            logger.info(f"Processing batch {batch_idx // batch_size + 1}/{total_batches}")
            
            if parallel:
                batch_results = self.process_batch_parallel(batch_data)
            else:
                batch_results = self.process_batch_sequential(batch_data)
            
            all_results.extend(batch_results)
            
            if progress_callback:
                progress_callback(len(all_results), len(df))
        
        # Convert results to DataFrame
        results_df = pd.DataFrame(all_results)
        return results_df
    
    def process_stream(self, data_stream, max_records: Optional[int] = None, 
                      batch_size: int = 10, progress_callback: Optional[Callable] = None):
        """
        Process a data stream in real-time
        
        Args:
            data_stream: Iterator/generator yielding data records
            max_records: Maximum number of records to process
            batch_size: Size of processing batches
            progress_callback: Optional callback for progress updates
            
        Yields:
            Detection results as they are processed
        """
        batch_buffer = []
        processed_count = 0
        
        for record in data_stream:
            if max_records and processed_count >= max_records:
                break
            
            batch_buffer.append(record)
            
            if len(batch_buffer) >= batch_size:
                # Process the batch
                results = self.process_batch_sequential(batch_buffer)
                
                for result in results:
                    yield result
                    processed_count += 1
                    
                    if progress_callback:
                        progress_callback(processed_count, max_records or "∞")
                
                batch_buffer = []
        
        # Process remaining records in buffer
        if batch_buffer:
            results = self.process_batch_sequential(batch_buffer)
            for result in results:
                yield result
                processed_count += 1
                
                if progress_callback:
                    progress_callback(processed_count, max_records or "∞")
    
    def _update_stats(self, batch_size: int, results: List[Dict[str, Any]], processing_time: float):
        """Update processing statistics"""
        anomaly_count = sum(1 for r in results if r.get("is_anomaly", False))
        
        self.processing_stats["total_batches"] += 1
        self.processing_stats["total_records"] += batch_size
        self.processing_stats["total_anomalies"] += anomaly_count
        self.processing_stats["total_processing_time"] += processing_time
        
        # Calculate averages
        self.processing_stats["avg_batch_time"] = (
            self.processing_stats["total_processing_time"] / self.processing_stats["total_batches"]
        )
        self.processing_stats["throughput_records_per_sec"] = (
            self.processing_stats["total_records"] / self.processing_stats["total_processing_time"]
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current processing statistics"""
        return self.processing_stats.copy()
    
    def reset_stats(self):
        """Reset processing statistics"""
        self.processing_stats = {
            "total_batches": 0,
            "total_records": 0,
            "total_anomalies": 0,
            "total_processing_time": 0.0,
            "avg_batch_time": 0.0,
            "throughput_records_per_sec": 0.0
        }
