2025-06-09 18:12:32,471 - __main__ - INFO - Dashboard script started/reloaded.
2025-06-09 18:12:33,080 - continuum5g_insight.core.utils - WARNING - pynvml not available. Using basic nvidia-smi check only.
2025-06-09 18:12:33,080 - continuum5g_insight.core.utils - WARNING - nvidia-smi command not found. NVIDIA GPU status cannot be determined.
2025-06-09 18:12:33,083 - continuum5g_insight.core.utils - WARNING - RAPIDS cuDF library not found. GPU acceleration for dataframe operations not available.
2025-06-09 18:12:33,083 - continuum5g_insight.core.utils - WARNING - pynvml not available. Using basic nvidia-smi check only.
2025-06-09 18:12:33,085 - continuum5g_insight.core.utils - WARNING - nvidia-smi command not found. NVIDIA GPU status cannot be determined.
2025-06-09 18:12:33,085 - continuum5g_insight.core.utils - INFO - CPU device selected for processing.
2025-06-09 18:12:39,400 - __main__ - INFO - Dashboard script started/reloaded.
2025-06-09 18:12:39,457 - __main__ - INFO - Initializing Continuum5G Insight system...
2025-06-09 18:12:39,469 - __main__ - INFO -    - NVIDIA Acceleration: False
2025-06-09 18:12:39,469 - __main__ - INFO -    - Processing Strategy: CPU-Only
2025-06-09 18:12:39,469 - __main__ - INFO -    - Batch Size: 10
2025-06-09 18:12:39,470 - __main__ - INFO - Initializing High-Volume Orchestrator for CPU batch processing...
2025-06-09 18:12:39,470 - continuum5g_insight.agents.continuum5g_insight.agents.high_volume_orchestrator - INFO - Initializing High-Volume Processing Orchestrator...
2025-06-09 18:12:39,473 - continuum5g_insight.core.utils - WARNING - pynvml not available. Using basic nvidia-smi check only.
2025-06-09 18:12:39,476 - continuum5g_insight.core.utils - WARNING - nvidia-smi command not found. NVIDIA GPU status cannot be determined.
2025-06-09 18:12:39,476 - continuum5g_insight.core.utils - INFO - CPU device selected for processing.
2025-06-09 18:12:39,476 - continuum5g_insight.agents.continuum5g_insight.agents.rapids_preprocessing_agent - INFO - Initializing RAPIDS Preprocessing Agent...
2025-06-09 18:12:39,478 - continuum5g_insight.core.utils - WARNING - pynvml not available. Using basic nvidia-smi check only.
2025-06-09 18:12:39,482 - continuum5g_insight.core.utils - WARNING - nvidia-smi command not found. NVIDIA GPU status cannot be determined.
2025-06-09 18:12:39,482 - continuum5g_insight.core.utils - INFO - CPU device selected for processing.
2025-06-09 18:12:39,482 - continuum5g_insight.agents.continuum5g_insight.agents.rapids_preprocessing_agent - INFO - Using pandas for CPU-based processing
2025-06-09 18:12:39,482 - continuum5g_insight.agents.continuum5g_insight.agents.rapids_preprocessing_agent - INFO - RAPIDS Preprocessing Agent initialized in cpu_pandas mode
2025-06-09 18:12:39,482 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Initializing AnomalyDetectionAgent...
2025-06-09 18:12:39,484 - continuum5g_insight.core.utils - WARNING - pynvml not available. Using basic nvidia-smi check only.
2025-06-09 18:12:39,487 - continuum5g_insight.core.utils - WARNING - nvidia-smi command not found. NVIDIA GPU status cannot be determined.
2025-06-09 18:12:39,487 - continuum5g_insight.core.utils - INFO - CPU device selected for processing.
2025-06-09 18:12:39,487 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load binary classification model from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:12:40,728 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded binary classification model from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:12:40,728 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load StandardScaler from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:12:40,728 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded StandardScaler from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:12:40,728 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load multiclass classification model from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:12:40,777 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded multiclass classification model from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:12:40,777 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load StandardScaler from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded StandardScaler from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - GPU acceleration not available or not detected. Using CPU-based models.
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - AnomalyDetectionAgent initialized.
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.high_volume_orchestrator - INFO - Orchestrator initialized with strategy: cpu_optimized
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.high_volume_orchestrator - INFO - Max workers: 4, GPU available: False
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.orchestration_agent - INFO - Initializing OrchestrationAgent...
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Initializing AnomalyDetectionAgent...
2025-06-09 18:12:40,779 - continuum5g_insight.core.utils - WARNING - pynvml not available. Using basic nvidia-smi check only.
2025-06-09 18:12:40,779 - continuum5g_insight.core.utils - WARNING - nvidia-smi command not found. NVIDIA GPU status cannot be determined.
2025-06-09 18:12:40,779 - continuum5g_insight.core.utils - INFO - CPU device selected for processing.
2025-06-09 18:12:40,779 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load binary classification model from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:12:40,824 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded binary classification model from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\rf_model_binary.joblib
2025-06-09 18:12:40,824 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load StandardScaler from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:12:40,825 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded StandardScaler from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_binary\standard_scaler.joblib
2025-06-09 18:12:40,825 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load multiclass classification model from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded multiclass classification model from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\rf_model_multiclass.joblib
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Attempting to load StandardScaler from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Successfully loaded StandardScaler from: C:\Users\<USER>\Documents\hg-hackathon25-track3\models\rf_models\model_multiclass\standard_scaler.joblib
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - GPU acceleration not available or not detected. Using CPU-based models.
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - AnomalyDetectionAgent initialized.
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Initializing ThreatAnalysisAgent...
2025-06-09 18:12:40,897 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Attempting to load knowledge base from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\continuum5g_insight\knowledge_bases\threat_actor_kb.json
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Successfully loaded knowledge base from: C:\Users\<USER>\Documents\hg-hackathon25-track3\continuum5g_insight\knowledge_bases\threat_actor_kb.json. Contains 3 entries.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat actor knowledge base loaded successfully with 4 enrichment rules, 3 device profiles, 2 IOCs.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - ThreatAnalysisAgent initialized.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Initializing ResponseStrategistAgent...
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Attempting to load knowledge base from resolved path: C:\Users\<USER>\Documents\hg-hackathon25-track3\continuum5g_insight\knowledge_bases\response_playbooks_kb.json
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Successfully loaded knowledge base from: C:\Users\<USER>\Documents\hg-hackathon25-track3\continuum5g_insight\knowledge_bases\response_playbooks_kb.json. Contains 1 entries.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response playbooks knowledge base loaded successfully with 4 playbooks.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - ResponseStrategistAgent initialized.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.orchestration_agent - INFO - OrchestrationAgent initialized with agent instances.
2025-06-09 18:12:40,910 - continuum5g_insight.agents.continuum5g_insight.agents.orchestration_agent - INFO - DataStreamer will look for datasets in: C:\Users\<USER>\Documents\hg-hackathon25-track3\data\sample_inputs
2025-06-09 18:12:40,910 - __main__ - INFO - Orchestrator initialized successfully.
2025-06-09 18:12:42,457 - __main__ - INFO - Using default file: C:\Users\<USER>\Documents\hg-hackathon25-track3\data\sample_inputs\5g_nidd_dataset.csv
2025-06-09 18:12:42,457 - __main__ - INFO - 'labelmalicious' column found in the dataset.
2025-06-09 18:12:42,457 - continuum5g_insight.agents.continuum5g_insight.agents.data_streaming_agent - INFO - Initializing DataStreamer...
2025-06-09 18:12:42,457 - continuum5g_insight.agents.continuum5g_insight.agents.data_streaming_agent - INFO - DataStreamer initialized with provided DataFrame. Shape: (415890, 48)
2025-06-09 18:12:42,457 - continuum5g_insight.agents.continuum5g_insight.agents.data_streaming_agent - INFO - Stream frequency set to 10 Hz (delay: 0.1000s).
2025-06-09 18:12:42,457 - continuum5g_insight.agents.continuum5g_insight.agents.data_streaming_agent - INFO - DataStreamer initialization complete.
2025-06-09 18:12:42,457 - __main__ - INFO - Starting processing loop for max 100 records. Mode: Manual Batch, RPS: 10.0
2025-06-09 18:12:42,468 - __main__ - INFO -    - Batch Size: 10
2025-06-09 18:12:42,481 - continuum5g_insight.agents.continuum5g_insight.agents.data_streaming_agent - INFO - Attempting to send 100 records...
2025-06-09 18:12:42,517 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,597 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,683 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,745 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,806 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,845 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,893 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:42,941 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:43,003 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:43,058 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:43,093 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,151 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,151 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,202 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,202 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,258 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,258 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,309 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,322 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,373 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,373 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,424 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,435 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,486 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,487 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,537 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,547 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,599 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,599 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,650 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,650 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,707 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,707 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,758 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,761 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,821 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,821 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,872 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,877 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:43,933 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:43,934 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:43,985 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:43,988 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:44,040 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:44,040 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:44,090 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:44,098 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:44,149 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:44,149 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:44,200 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:45,974 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,065 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,143 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,227 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,322 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,411 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,503 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,593 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,670 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,746 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:46,810 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:46,871 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:46,871 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:46,922 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:46,932 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:46,983 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:46,983 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,034 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,036 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,091 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,092 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,143 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,152 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,203 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,203 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,255 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,266 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,317 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,317 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,369 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,382 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,434 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,434 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,485 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,496 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,547 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,547 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,598 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,607 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,658 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,660 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,711 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,722 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,773 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,774 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,824 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:47,835 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:47,888 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:47,888 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:47,940 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:48,271 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:48,411 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:48,535 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:48,673 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:48,795 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:48,916 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:49,011 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:49,116 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:49,195 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:49,311 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:49,395 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:49,452 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:49,452 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:49,504 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:49,515 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:49,566 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:49,566 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:49,617 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:49,627 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:49,678 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:49,679 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:49,729 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:49,740 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:49,791 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:49,792 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:49,843 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:49,844 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:49,904 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:49,905 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:49,956 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:49,966 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:50,016 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:50,016 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:50,074 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:50,093 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:50,145 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:50,145 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:50,199 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:50,210 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:50,261 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:50,261 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:50,312 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:50,321 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:50,372 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:50,373 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:50,424 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:50,435 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:50,485 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:50,485 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:50,537 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:50,854 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:50,969 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,085 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,218 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,363 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,499 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,627 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,760 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:51,892 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:52,013 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:52,118 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,171 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,172 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,223 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,229 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,285 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,285 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,336 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,345 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,397 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,397 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,449 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,453 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,505 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,506 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,556 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,564 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,616 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,616 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,667 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,676 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,727 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,728 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,779 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,789 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,840 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,840 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:52,892 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:52,900 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:52,953 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:52,953 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:53,004 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:53,013 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:53,064 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:53,064 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:53,115 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:53,127 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:53,178 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:53,178 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:53,233 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:53,613 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:53,732 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:53,846 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:53,949 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,060 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,142 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,258 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,355 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,469 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,569 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:54,651 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:54,703 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:54,703 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:54,754 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:54,762 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:54,813 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:54,813 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:54,864 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:54,864 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:54,925 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:54,925 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:54,976 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:54,983 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,035 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,035 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,086 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:55,095 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,147 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,147 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,198 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:55,202 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,257 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,258 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,309 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:55,316 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,367 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,367 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,419 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:55,426 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,479 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,479 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,531 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:55,542 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,594 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,594 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,645 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:55,655 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:55,706 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:55,707 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:55,757 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:56,123 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,242 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,343 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,459 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,559 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,674 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,773 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,874 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:56,980 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:57,095 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:57,195 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,247 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,247 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,298 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,304 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,354 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,354 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,406 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,408 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,463 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,464 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,514 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,522 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,573 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,573 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,624 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,629 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,687 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,687 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,738 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,742 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,796 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,796 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,847 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,852 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:57,903 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:57,904 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:57,955 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:57,959 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:58,014 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:58,014 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:58,065 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:58,070 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:58,121 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:58,121 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:58,172 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:58,175 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:58,230 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:58,230 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:58,280 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:58,587 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:58,668 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:58,759 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:58,868 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:58,938 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:59,035 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:59,107 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:59,191 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:59,277 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:59,342 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:12:59,419 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:59,474 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:59,474 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:59,525 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:59,527 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:59,582 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:59,582 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:59,633 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:59,641 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:59,691 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:59,691 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:59,742 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:59,744 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:59,799 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:59,799 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:59,850 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:59,854 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:12:59,905 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:12:59,905 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:12:59,956 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:12:59,957 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:00,015 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:00,015 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:00,066 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:00,072 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:00,123 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:00,123 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:00,174 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:00,174 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:00,230 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:00,230 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:00,281 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:00,286 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:00,337 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:00,337 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:00,388 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:00,390 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:00,447 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:00,447 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:00,499 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:00,811 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:00,892 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:00,977 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,087 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,142 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,234 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,309 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,359 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,455 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,530 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:01,593 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:01,647 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:01,647 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:01,698 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:01,704 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:01,755 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:01,755 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:01,807 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:01,812 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:01,863 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:01,863 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:01,914 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:01,919 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:01,970 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:01,970 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,021 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:02,027 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:02,077 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:02,078 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,128 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:02,137 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:02,188 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:02,188 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,239 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:02,241 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:02,296 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:02,296 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,347 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:02,354 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:02,406 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:02,407 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,458 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:02,461 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:02,514 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:02,514 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,565 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:02,575 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:02,627 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:02,627 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:02,678 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:03,056 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,221 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,324 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,417 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,513 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,591 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,678 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,758 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,843 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,924 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:03,995 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,054 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,054 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,105 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,112 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,163 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,163 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,213 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,220 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,271 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,271 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,322 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,330 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,382 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,382 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,433 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,433 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,489 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,489 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,541 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,546 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,597 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,597 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,648 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,648 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,705 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,705 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,756 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,762 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,813 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,813 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,864 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,869 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:04,921 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:04,921 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:04,972 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:04,975 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:05,030 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:05,030 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:05,081 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:05,449 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:05,548 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:05,644 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:05,706 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:05,798 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:05,875 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:05,939 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:06,036 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:06,111 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:06,174 - continuum5g_insight.agents.continuum5g_insight.agents.anomaly_detection_agent - INFO - Binary prediction is 1 (anomaly), proceeding to multiclass classification.
2025-06-09 18:13:06,258 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,310 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,310 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:06,361 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:06,369 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,420 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,420 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:06,471 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:06,475 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,526 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,526 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:06,578 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:06,582 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,633 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,633 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:06,684 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:06,689 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,741 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,742 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:06,793 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:06,800 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,851 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,851 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:06,903 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:06,914 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:06,964 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:06,964 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:07,015 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:07,021 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:07,072 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:07,073 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:07,126 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:07,133 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:07,184 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:07,184 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:07,235 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:07,246 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - --- SIMULATING SLM Call for ThreatAnalysisRAG ---
2025-06-09 18:13:07,297 - continuum5g_insight.agents.continuum5g_insight.agents.threat_analysis_agent - INFO - Threat analysis generated: Denial of Service (DoS) activity detected. System resources may be impacted. Immediate attention required. (Priority: High)
2025-06-09 18:13:07,298 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - --- SIMULATING SLM Call for ResponseStrategyRAG ---
2025-06-09 18:13:07,349 - continuum5g_insight.agents.continuum5g_insight.agents.response_strategist_agent - INFO - Response strategy generated: Playbook for UDPFlood
2025-06-09 18:13:07,869 - continuum5g_insight.agents.continuum5g_insight.agents.data_streaming_agent - INFO - Finished sending 100 of 100 requested records. Current index: 100
2025-06-09 18:13:07,869 - __main__ - INFO - Finished processing 100 requests.
2025-06-09 18:13:08,377 - __main__ - INFO - Dashboard script started/reloaded.
