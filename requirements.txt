# Core utilities
pandas
numpy
scikit-learn==1.2.2
joblib

# NVIDIA GPU acceleration libraries
cudf-cu12>=23.12.0  # RAPIDS cuDF for GPU-accelerated data processing
cuml-cu12>=23.12.0  # RAPIDS cuML for GPU-accelerated ML inference
cupy-cuda12x>=12.0.0  # CuPy for GPU array operations
pynvml>=11.5.0  # NVIDIA Management Library for GPU monitoring

# NVIDIA AIQ Toolkit (if specific packages are known, otherwise placeholder)
# nvidia-aiq-toolkit # Placeholder, replace with actual if available

# NeMo and related (for SLMs - might be extensive, initial thoughts)
# nemo_toolkit[all] # Placeholder, actual dependencies might differ based on SLM choice / NIM usage

# For potential API/communication between agents (optional, can be added later)
# fastapi
# uvicorn

# Dashboard
streamlit

# Other utilities
python-dotenv
# Add other libraries as they become necessary
