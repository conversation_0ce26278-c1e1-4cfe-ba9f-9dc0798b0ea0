#!/usr/bin/env python3
"""
Quick Demo Script
Demonstrates the 5G Security Analytics system capabilities
"""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.data_loader import DataLoader
from src.models.anomaly_detector import AnomalyDetector
from src.core.batch_processor import BatchProcessor
from src.utils.metrics import calculate_detection_metrics


def demo_single_prediction():
    """Demo single record prediction"""
    print("🔍 Demo: Single Record Prediction")
    print("=" * 50)
    
    # Initialize detector
    detector = AnomalyDetector("models/rf_models")
    
    # Sample network data (simulated)
    sample_data = {
        'seq': 1500, 'offset': 0, 'sttl': 64, 'ackdat': 1000, 'tcprtt': 0.05,
        'smeanpktsz': 1200, 'shops': 443, 'dttl': 64, 'srcbytes': 5000,
        'totbytes': 10000, 'dmeanpktsz': 1200, 'srcwin': 65535, 'stos': 0,
        'srctcpbase': 2000, 'dstloss': 0, 'loss': 0
    }
    
    print("Processing network traffic record...")
    result = detector.detect_anomaly(sample_data)
    
    print(f"✅ Analysis Complete:")
    print(f"   🚨 Anomaly Detected: {result['is_anomaly']}")
    print(f"   🎯 Attack Type: {result['attack_type']}")
    print(f"   📊 Confidence: {result['binary_confidence']:.3f}")
    print(f"   ⚡ Processing Time: {result['total_processing_time_ms']:.1f}ms")
    print()


def demo_batch_processing():
    """Demo batch processing"""
    print("🔄 Demo: Batch Processing")
    print("=" * 50)
    
    # Initialize components
    loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
    detector = AnomalyDetector("models/rf_models")
    processor = BatchProcessor(detector)
    
    print(f"📊 Dataset: {loader.get_dataset_info()['total_records']:,} records")
    print("Processing batch of 100 records...")
    
    # Get and process batch
    batch_df = loader.get_batch(100)
    batch_data = batch_df.to_dict('records')
    
    start_time = time.time()
    results = processor.process_batch_sequential(batch_data)
    processing_time = time.time() - start_time
    
    # Analyze results
    anomaly_count = sum(1 for r in results if r.get("is_anomaly", False))
    attack_types = {}
    for result in results:
        if result.get("is_anomaly", False):
            attack_type = result.get("attack_type", "Unknown")
            attack_types[attack_type] = attack_types.get(attack_type, 0) + 1
    
    print(f"✅ Batch Processing Complete:")
    print(f"   📈 Processed: {len(results)} records")
    print(f"   🚨 Anomalies: {anomaly_count}")
    print(f"   📊 Anomaly Rate: {(anomaly_count/len(results)*100):.1f}%")
    print(f"   ⚡ Throughput: {len(results)/processing_time:.1f} records/sec")
    print(f"   🎯 Attack Types Detected:")
    for attack_type, count in attack_types.items():
        print(f"      - {attack_type}: {count}")
    print()


def demo_performance_metrics():
    """Demo performance metrics calculation"""
    print("📈 Demo: Performance Metrics")
    print("=" * 50)
    
    # Initialize components
    loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
    detector = AnomalyDetector("models/rf_models")
    processor = BatchProcessor(detector)
    
    print("Processing multiple batches for performance analysis...")
    
    all_results = []
    total_time = 0
    
    for batch_num in range(3):
        batch_df = loader.get_batch(50)
        batch_data = batch_df.to_dict('records')
        
        start_time = time.time()
        results = processor.process_batch_sequential(batch_data)
        batch_time = time.time() - start_time
        
        all_results.extend(results)
        total_time += batch_time
        
        print(f"   Batch {batch_num + 1}: {len(results)} records in {batch_time:.2f}s")
    
    # Calculate metrics
    if all_results:
        metrics = calculate_detection_metrics(all_results)
        stats = processor.get_stats()
        
        print(f"✅ Performance Analysis:")
        print(f"   📊 Total Records: {len(all_results)}")
        print(f"   ⚡ Overall Throughput: {len(all_results)/total_time:.1f} records/sec")
        print(f"   🎯 Detection Accuracy: {metrics.get('accuracy', 0):.3f}")
        print(f"   📈 Precision: {metrics.get('precision', 0):.3f}")
        print(f"   📉 Recall: {metrics.get('recall', 0):.3f}")
        print(f"   🏆 F1 Score: {metrics.get('f1_score', 0):.3f}")
    print()


def demo_real_time_simulation():
    """Demo real-time processing simulation"""
    print("🔴 Demo: Real-time Processing Simulation")
    print("=" * 50)
    
    # Initialize components
    loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
    detector = AnomalyDetector("models/rf_models")
    
    print("Simulating real-time network traffic analysis...")
    print("Processing 20 records with 0.5s intervals...")
    
    anomaly_count = 0
    for i, record in enumerate(loader.stream_records(max_records=20)):
        result = detector.detect_anomaly(record)
        
        status = "🚨 ANOMALY" if result['is_anomaly'] else "✅ NORMAL"
        attack_type = result.get('attack_type', 'Normal')
        confidence = result.get('binary_confidence', 0)
        
        print(f"   Record {i+1:2d}: {status} | {attack_type:12s} | Confidence: {confidence:.3f}")
        
        if result['is_anomaly']:
            anomaly_count += 1
        
        time.sleep(0.5)  # Simulate real-time delay
    
    print(f"✅ Real-time Simulation Complete:")
    print(f"   🚨 Total Anomalies: {anomaly_count}/20")
    print(f"   📊 Detection Rate: {(anomaly_count/20)*100:.1f}%")
    print()


def main():
    """Run all demos"""
    print("🛡️ 5G Security Analytics Platform - Demo")
    print("=" * 60)
    print("This demo showcases the capabilities of the security analytics system")
    print()
    
    demos = [
        demo_single_prediction,
        demo_batch_processing,
        demo_performance_metrics,
        demo_real_time_simulation
    ]
    
    for i, demo_func in enumerate(demos, 1):
        print(f"Demo {i}/{len(demos)}")
        demo_func()
        
        if i < len(demos):
            input("Press Enter to continue to next demo...")
            print()
    
    print("🎉 Demo Complete!")
    print("To launch the interactive dashboard, run: python app.py")


if __name__ == "__main__":
    main()
