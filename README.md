# 🛡️ 5G Security Analytics Platform

A real-time anomaly detection system for 5G network traffic using RandomForest models and Gradio dashboard.

## 🚀 Features

- **Real-time Anomaly Detection**: Binary and multiclass classification using RandomForest models
- **Interactive Dashboard**: Modern Gradio-based web interface
- **Batch Processing**: Efficient processing of large datasets
- **Performance Monitoring**: Real-time metrics and throughput tracking
- **Attack Classification**: Detects UDPFlood, HTTPFlood, SYNScan, and other attack types
- **GPU Ready**: Optional NVIDIA RAPIDS support for acceleration

## 📁 Project Structure

```
├── src/
│   ├── core/
│   │   ├── data_loader.py      # Dataset loading and streaming
│   │   └── batch_processor.py  # Batch processing engine
│   ├── models/
│   │   └── anomaly_detector.py # RandomForest-based detection
│   ├── ui/
│   │   └── dashboard.py        # Gradio dashboard interface
│   └── utils/
│       └── metrics.py          # Performance metrics
├── models/
│   └── rf_models/              # Pre-trained RandomForest models
├── data/
│   └── sample_inputs/          # 5G-NIDD dataset
├── reference_project/          # Original implementation reference
├── app.py                      # Main application
├── test_system.py             # System tests
└── requirements.txt           # Dependencies
```

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- NVIDIA GPU (optional, for acceleration)

### Setup

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd hg-hackathon25-track3
   pip install -r requirements.txt
   ```

2. **Verify installation**:
   ```bash
   python test_system.py
   ```

## 🚀 Quick Start

### Launch Control Panel (Gradio)
```bash
python app.py
```
Access the control panel at `http://localhost:7860`

### Launch Monitoring Dashboard (Streamlit)
```bash
streamlit run dashboard.py
```
Access the dashboard at `http://localhost:8501`

### Complete Setup
1. **Start Control Panel**: `python app.py` (Port 7860)
2. **Start Dashboard**: `streamlit run dashboard.py` (Port 8501, another terminal)
3. **Configure & Start**: Use Gradio panel to configure and start processing
4. **Monitor**: Watch real-time results in Streamlit dashboard

### Quick Start (Both at once)
```bash
python start_system.py
```

### Command Line Options
```bash
python app.py --help
python app.py --host 0.0.0.0 --port 8080 --share
```

## 📊 Interface Features

### Gradio Control Panel (Port 7860)
- **System Status**: Dataset and model loading status
- **Processing Configuration**: Batch size, processing rate, mode selection
- **Control Buttons**: Start/Stop/Clear processing
- **Statistics Display**: Current processing metrics
- **Settings Management**: All processing parameters

### Streamlit Dashboard (Port 8501)
- **Real-time Charts**: Anomaly timeline, attack type distribution
- **Live Metrics**: Processing rate, anomaly rate, throughput
- **Performance Monitoring**: Real-time processing statistics
- **Recent Detections**: Latest anomaly details with confidence scores
- **System Logs**: Live processing logs and status updates
- **Auto-refresh**: Updates every 2 seconds automatically

## 🤖 Models

The system uses pre-trained RandomForest models:

- **Binary Classification**: Normal vs Anomaly detection
- **Multiclass Classification**: Attack type identification
  - UDPFlood, HTTPFlood, SYNScan, TCPConnectScan
  - SYNFlood, ICMPFlood, SlowrateDoS, UDPScan

### Model Performance
- **Processing Speed**: ~100-500 records/second
- **Accuracy**: High precision anomaly detection
- **Real-time**: Sub-millisecond inference per record

## 📈 Performance

### Throughput Metrics
- **CPU Mode**: 100-300 records/second
- **Batch Processing**: Optimized for high-volume data
- **Memory Efficient**: Streaming processing for large datasets

### Monitoring
- Real-time throughput calculation
- Processing time tracking
- Memory usage monitoring
- Performance statistics

## 🔧 API Usage

### Basic Usage
```python
from src.models.anomaly_detector import AnomalyDetector
from src.core.data_loader import DataLoader

# Initialize components
detector = AnomalyDetector("models/rf_models")
loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")

# Process single record
sample_data = {...}  # Your network data
result = detector.detect_anomaly(sample_data)
print(f"Anomaly: {result['is_anomaly']}, Type: {result['attack_type']}")
```

### Batch Processing
```python
from src.core.batch_processor import BatchProcessor

processor = BatchProcessor(detector)
batch_data = loader.get_batch(100)
results = processor.process_batch_sequential(batch_data.to_dict('records'))
```

## 📚 Reference

The `reference_project/` directory contains the original Streamlit-based implementation with advanced features including:
- Multi-agent architecture
- SLM integration
- RAG capabilities
- NVIDIA RAPIDS support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
