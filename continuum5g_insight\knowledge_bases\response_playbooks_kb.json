{"playbooks": [{"conditions": {"multiclass_prediction_contains": ["SYNScan", "TCPConnectScan", "UDPScan"], "priority_assessment_is": ["Medium", "High"]}, "response_steps": [{"step_id": "SCAN_RESP_1", "action": "Log detailed information for the source IP and targeted ports.", "description": "Gather comprehensive data for forensic analysis and pattern detection.", "priority_order": 1, "justification": "Essential for understanding the scope and nature of the reconnaissance."}, {"step_id": "SCAN_RESP_2", "action": "Increase monitoring level for the source IP for the next 24 hours.", "description": "Observe for follow-up activities or escalation.", "priority_order": 2, "justification": "Scanning is often a precursor to more direct attacks."}, {"step_id": "SCAN_RESP_3", "action": "If scanning is aggressive or targets critical services, consider implementing a temporary block on the source IP at the network perimeter (firewall/router).", "description": "Proactively prevent further reconnaissance or exploitation attempts from a confirmed hostile scanner.", "priority_order": 3, "justification": "Mitigate risk from persistent or high-risk scanning."}]}, {"conditions": {"multiclass_prediction_contains": ["SYNFlood", "UDPFlood", "ICMPFlood", "HTTPFlood"], "priority_assessment_is": ["High"]}, "response_steps": [{"step_id": "DOS_RESP_1", "action": "Verify and activate pre-configured DoS mitigation measures (e.g., rate limiting, SYN cookies on affected 5G network functions like UPF or security gateways).", "description": "Engage automated defenses to absorb or deflect the attack.", "priority_order": 1, "justification": "Immediate action to protect service availability."}, {"step_id": "DOS_RESP_2", "action": "Identify primary source IPs/subnets of the attack and apply null routing or blacklisting at the network edge or via BGP if applicable.", "description": "Cut off attack traffic as close to the source as possible.", "priority_order": 2, "justification": "Reduce load on internal systems and mitigation tools."}, {"step_id": "DOS_RESP_3", "action": "Alert 5G Core security team and network operations. If attack exceeds local mitigation capacity, contact upstream ISP or DDoS mitigation service.", "description": "Escalate for broader response and specialized assistance.", "priority_order": 3, "justification": "Coordinate efforts for large-scale or persistent attacks."}]}, {"conditions": {"multiclass_prediction_contains": ["SlowrateDoS"], "priority_assessment_is": ["High"]}, "response_steps": [{"step_id": "SLOWDOS_RESP_1", "action": "Analyze traffic patterns for connections with long durations and low data rates targeting specific services.", "description": "Identify characteristics of the slow DoS attack.", "priority_order": 1, "justification": "Understand attack vector for targeted mitigation."}, {"step_id": "SLOWDOS_RESP_2", "action": "Adjust server/application timeouts and connection limits to be more resilient against resource exhaustion.", "description": "Harden services against slow connection attacks.", "priority_order": 2, "justification": "Reduce effectiveness of resource depletion techniques."}, {"step_id": "SLOWDOS_RESP_3", "action": "Implement tarpitting or connection reset mechanisms for identified malicious slow connections.", "description": "Actively disrupt attack connections.", "priority_order": 3, "justification": "Free up resources and deter attacker."}]}, {"conditions": {"is_binary_anomaly_only": true, "priority_assessment_is": ["Low", "Medium"]}, "response_steps": [{"step_id": "GEN_ANOM_RESP_1", "action": "Capture detailed packet logs for the anomalous traffic flow.", "description": "Collect data for manual investigation.", "priority_order": 1, "justification": "Needed for deeper analysis of unknown anomalies."}, {"step_id": "GEN_ANOM_RESP_2", "action": "Correlate the timing and characteristics of the anomaly with any recent network changes or known benign activities.", "description": "Rule out operational causes or known non-malicious anomalies.", "priority_order": 2, "justification": "Reduce false positives and unnecessary escalations."}, {"step_id": "GEN_ANOM_RESP_3", "action": "If the anomaly persists or its characteristics are suspicious after initial review, escalate to a security analyst for deeper investigation.", "description": "Ensure expert review for potentially novel threats.", "priority_order": 3, "justification": "Human expertise required for ambiguous situations."}]}]}