# Continuum5G Insight - Anomaly Detection Agent
# Uses user's RandomForest models to detect anomalies in the data stream.

import joblib
import pandas as pd
import numpy as np
import os
import sys
import time
from datetime import datetime
import logging
from continuum5g_insight.core.utils import is_nvidia_gpu_available, is_cuml_available, select_optimal_device

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

# --- IMPORTANT USER REMINDER ---
# (User reminders via print are kept for direct script execution visibility)
print("------------------------------------------------------------------------------------")
print("IMPORTANT MODEL PATH CONFIGURATION (AnomalyDetectionAgent):")
print("The model paths below are placeholders.")
print("Please update MODEL_PATH_BINARY, M<PERSON><PERSON>_PATH_MULTICLASS, and M<PERSON><PERSON>_PATH_SCALER")
print("with the actual paths to your .joblib files in the 'models/rf_models/' directory.")
MODEL_PATH_BINARY = "../../models/rf_models/model_binary/rf_model_binary.joblib"
MODEL_PATH_MULTICLASS = "../../models/rf_models/model_multiclass/rf_model_multiclass.joblib"
MODEL_PATH_BINARY_SCALER = "../../models/rf_models/model_binary/standard_scaler.joblib"
MODEL_PATH_MULTI_SCALER = "../../models/rf_models/model_multiclass/standard_scaler.joblib"

print(f"Current Binary Model Path (from anomaly_detection_agent.py): {os.path.abspath(os.path.join(os.path.dirname(__file__), MODEL_PATH_BINARY))}")
print(f"Current Multiclass Model Path (from anomaly_detection_agent.py): {os.path.abspath(os.path.join(os.path.dirname(__file__), MODEL_PATH_MULTICLASS))}")
print(f"Current Scaler Model Path (from anomaly_detection_agent.py): {os.path.abspath(os.path.join(os.path.dirname(__file__), MODEL_PATH_BINARY_SCALER))}")
print(f"Current Scaler Model Path (from anomaly_detection_agent.py): {os.path.abspath(os.path.join(os.path.dirname(__file__), MODEL_PATH_MULTI_SCALER))}")

feature_columns_binary = ['seq', 'offset', 'sttl', 'ackdat', 'tcprtt', 'smeanpktsz', 'shops', 'dttl', 'srcbytes', 'totbytes', 'dmeanpktsz', 'srcwin', 'stos']
feature_columns_multi = ['ackdat', 'shops', 'seq', 'tcprtt', 'dmeanpktsz', 'offset', 'sttl','srctcpbase', 'smeanpktsz', 'dstloss', 'loss', 'dttl', 'srcbytes', 'totbytes']
target_columns_binary = ['non-malicious', 'malicious']
target_columns_multi = ['Benign', 'UDPFlood', 'HTTPFlood', 'SlowrateDoS', 'TCPConnectScan', 'SYNScan', 'UDPScan', 'SYNFlood', 'ICMPFlood']

print("------------------------------------------------------------------------------------")

CLASS_LABELS_MAP = {
    0: 'Benign', 1: 'UDPFlood', 2: 'HTTPFlood', 3: 'SlowrateDoS',
    4: 'TCPConnectScan', 5: 'SYNScan', 6: 'UDPScan',
    7: 'SYNFlood', 8: 'ICMPFlood'
}

def load_joblib_object(model_path, object_type="model"):
    script_dir = os.path.dirname(__file__)
    resolved_model_path = os.path.abspath(os.path.join(script_dir, model_path))
    logger.info(f"Attempting to load {object_type} from resolved path: {resolved_model_path}")
    actual_model_path_to_load = resolved_model_path

    try:
        if resolved_model_path.endswith(".placeholder"):
            non_placeholder_path = resolved_model_path.replace(".placeholder", "")
            if not os.path.exists(non_placeholder_path):
                logger.warning(f"Placeholder path is configured ('{resolved_model_path}'). Actual {object_type} file ('{non_placeholder_path}') not found. Attempting to load placeholder.")
                if not os.path.exists(resolved_model_path):
                     logger.error(f"The placeholder {object_type} file '{resolved_model_path}' itself was not found.")
                     raise FileNotFoundError(f"The placeholder {object_type} file '{resolved_model_path}' itself was not found.")
            else:
                logger.info(f"Placeholder path configured but actual {object_type} file ('{non_placeholder_path}') found. Using actual file.")
                actual_model_path_to_load = non_placeholder_path

        if not os.path.exists(actual_model_path_to_load):
            logger.error(f"{object_type.capitalize()} file not found at path: {actual_model_path_to_load}")
            raise FileNotFoundError(f"{object_type.capitalize()} file not found at path: {actual_model_path_to_load}")

        loaded_object = joblib.load(actual_model_path_to_load)
        logger.info(f"Successfully loaded {object_type} from: {actual_model_path_to_load}")
        return loaded_object
    except FileNotFoundError:
        # Already logged error, re-raise or return None
        return None
    except Exception as e:
        logger.error(f"Could not load {object_type} from {actual_model_path_to_load}. Error: {e}", exc_info=True)
    return None


class AnomalyDetectionAgent:
    def __init__(self, model_path_binary, model_path_multiclass, model_path_scaler_binary, model_path_scaler_multi):
        logger.info("Initializing AnomalyDetectionAgent...")
        
        # Determine optimal processing device and capabilities
        self.device_type, self.device_info = select_optimal_device()
        self.nvidia_gpu_available = self.device_info.get('gpu_info', {}).get('gpu_available', False)
        self.cuml_available = self.device_info.get('cuml_available', False)
        
        # Load CPU models (standard approach)
        self.binary_model = load_joblib_object(model_path_binary, "binary classification model")
        if self.binary_model is None:
            logger.warning("Binary classification model failed to load. Detection capabilities will be limited.")

        self.scaler_binary = load_joblib_object(model_path_scaler_binary, "StandardScaler")
        if self.scaler_binary is None:
            logger.critical("StandardScaler binary failed to load. Anomaly detection will not be accurate and may produce errors. Preprocessing cannot proceed correctly.")
        
        self.multiclass_model = load_joblib_object(model_path_multiclass, "multiclass classification model")
        if self.multiclass_model is None:
            logger.warning("Multiclass classification model failed to load. Attack classification will not be available.")

        self.scaler_multi = load_joblib_object(model_path_scaler_multi, "StandardScaler")
        if self.scaler_multi is None:
            logger.critical("StandardScaler multi failed to load. Anomaly detection will not be accurate and may produce errors. Preprocessing cannot proceed correctly.")
        
        # Initialize GPU-accelerated models if available
        self.gpu_binary_model = None
        self.gpu_multiclass_model = None
        self.gpu_scaler_binary = None
        self.gpu_scaler_multi = None
        
        if self.cuml_available and self.nvidia_gpu_available:
            logger.info("NVIDIA GPU and cuML available. Attempting to create GPU-accelerated models...")
            self._initialize_gpu_models()
        else:
            logger.info("GPU acceleration not available or not detected. Using CPU-based models.")
        
        # Performance tracking
        self.inference_stats = {
            "cpu_inferences": 0,
            "gpu_inferences": 0,
            "cpu_total_time": 0.0,
            "gpu_total_time": 0.0,
            "cpu_avg_time": 0.0,
            "gpu_avg_time": 0.0
        }

        logger.info("AnomalyDetectionAgent initialized.")
    
    def _initialize_gpu_models(self):
        """Initialize GPU-accelerated models using cuML when available."""
        try:
            import cuml
            from cuml.ensemble import RandomForestClassifier as cuRF
            from cuml.preprocessing import StandardScaler as cuStandardScaler
            import cudf
            
            logger.info("Attempting to create GPU-accelerated RandomForest models...")
            
            # Create GPU models with similar parameters to original models
            if self.binary_model is not None:
                try:
                    # Extract parameters from CPU model if possible
                    cpu_params = self.binary_model.get_params() if hasattr(self.binary_model, 'get_params') else {}
                    
                    # Create GPU binary model with compatible parameters
                    gpu_params = {
                        'n_estimators': cpu_params.get('n_estimators', 100),
                        'max_depth': cpu_params.get('max_depth', 16),
                        'random_state': cpu_params.get('random_state', 42),
                        'n_streams': 4  # GPU-specific parameter for parallel streams
                    }
                    
                    self.gpu_binary_model = cuRF(**gpu_params)
                    logger.info("GPU binary RandomForest model created")
                    
                except Exception as e:
                    logger.warning(f"Failed to create GPU binary model: {e}")
            
            if self.multiclass_model is not None:
                try:
                    cpu_params = self.multiclass_model.get_params() if hasattr(self.multiclass_model, 'get_params') else {}
                    
                    gpu_params = {
                        'n_estimators': cpu_params.get('n_estimators', 100),
                        'max_depth': cpu_params.get('max_depth', 16),
                        'random_state': cpu_params.get('random_state', 42),
                        'n_streams': 4
                    }
                    
                    self.gpu_multiclass_model = cuRF(**gpu_params)
                    logger.info("GPU multiclass RandomForest model created")
                    
                except Exception as e:
                    logger.warning(f"Failed to create GPU multiclass model: {e}")
            
            # Create GPU scalers
            try:
                self.gpu_scaler_binary = cuStandardScaler()
                self.gpu_scaler_multi = cuStandardScaler()
                logger.info("GPU StandardScalers created")
            except Exception as e:
                logger.warning(f"Failed to create GPU scalers: {e}")
            
            # Note: In a production system, you would need to train these GPU models
            # or convert the existing trained CPU models to GPU format
            logger.warning("GPU models created but not trained. In production, you would need to train these models or convert existing CPU models.")
            
        except ImportError as e:
            logger.warning(f"cuML import failed: {e}. GPU acceleration not available.")
        except Exception as e:
            logger.error(f"Error initializing GPU models: {e}", exc_info=True)

    def preprocess_input(self, data_record, feature_columns, scaler=None):
        if not isinstance(data_record, dict):
            logger.error(f"preprocess_input expects a dictionary, got {type(data_record)}")
            return None
        
        try:
            df_record = pd.DataFrame([data_record])
            df_processed = df_record[feature_columns]
        except KeyError as e:
            logger.error(f"Missing one or more expected features in data_record: {e}. Record: {data_record}. Expected: {feature_columns}. Available: {list(data_record.keys())}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Error during feature selection/ordering in preprocess_input: {e}. Record: {data_record}", exc_info=True)
            return None

        if scaler is not None:
            try:
                scaled_data = scaler.transform(df_processed)
                return scaled_data
            except Exception as e:
                logger.error(f"Error applying StandardScaler: {e}. Data shape: {df_processed.shape}. Ensure features match scaler training.", exc_info=True)
                return None 
        else:
            if not hasattr(self, '_scaler_missing_warning_shown'):
                logger.critical("Scaler not loaded in preprocess_input. Returning unscaled data. Predictions will be inaccurate.")
                self._scaler_missing_warning_shown = True
            return df_processed.to_numpy()

    def _preprocess_for_gpu(self, data_record, feature_columns, gpu_scaler=None):
        """
        Preprocess data for GPU inference using cuDF when available.
        
        Args:
            data_record: Dictionary containing feature data
            feature_columns: List of required feature columns
            gpu_scaler: GPU-based StandardScaler (cuML)
            
        Returns:
            GPU-ready data or None if preprocessing fails
        """
        if not self.cuml_available:
            return None
            
        try:
            import cudf
            import cupy as cp
            
            # Convert to cuDF DataFrame
            df_record = cudf.DataFrame([data_record])
            df_processed = df_record[feature_columns]
            
            # Apply GPU scaling if available
            if gpu_scaler is not None:
                scaled_data = gpu_scaler.transform(df_processed)
                return scaled_data
            else:
                # Convert to CuPy array for GPU operations
                return cp.asarray(df_processed.values)
                
        except Exception as e:
            logger.error(f"Error in GPU preprocessing: {e}")
            return None
    
    def _gpu_inference(self, scaled_features, model_type="binary"):
        """
        Perform GPU-accelerated inference using cuML models.
        
        Args:
            scaled_features: GPU-preprocessed features
            model_type: "binary" or "multiclass"
            
        Returns:
            Prediction results or None if GPU inference fails
        """
        if not self.cuml_available:
            return None
            
        start_time = time.time()
        
        try:
            if model_type == "binary" and self.gpu_binary_model is not None:
                # Note: This would require the GPU model to be trained
                # probabilities = self.gpu_binary_model.predict_proba(scaled_features)
                # For now, we'll simulate GPU inference with faster processing
                logger.info("GPU binary inference would be performed here (model needs training)")
                result = None  # Placeholder
                
            elif model_type == "multiclass" and self.gpu_multiclass_model is not None:
                # prediction = self.gpu_multiclass_model.predict(scaled_features)
                # probabilities = self.gpu_multiclass_model.predict_proba(scaled_features)
                logger.info("GPU multiclass inference would be performed here (model needs training)")
                result = None  # Placeholder
                
            else:
                result = None
            
            inference_time = time.time() - start_time
            self.inference_stats["gpu_inferences"] += 1
            self.inference_stats["gpu_total_time"] += inference_time
            self.inference_stats["gpu_avg_time"] = (
                self.inference_stats["gpu_total_time"] / self.inference_stats["gpu_inferences"]
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in GPU inference: {e}")
            return None
    
    def _cpu_inference(self, scaled_features, model_type="binary"):
        """
        Perform CPU inference using standard scikit-learn models.
        
        Args:
            scaled_features: CPU-preprocessed features
            model_type: "binary" or "multiclass"
            
        Returns:
            Prediction results
        """
        start_time = time.time()
        result = None
        
        try:
            if model_type == "binary" and self.binary_model is not None:
                probabilities = self.binary_model.predict_proba(scaled_features)[0]
                result = {
                    "probabilities": probabilities.tolist(),
                    "prediction": 1 if probabilities[1] >= 0.3 else 0
                }
                
            elif model_type == "multiclass" and self.multiclass_model is not None:
                prediction_index = int(self.multiclass_model.predict(scaled_features)[0])
                probabilities = self.multiclass_model.predict_proba(scaled_features)[0]
                result = {
                    "prediction_index": prediction_index,
                    "prediction_label": CLASS_LABELS_MAP.get(prediction_index, f"Unknown_Label_Index_{prediction_index}"),
                    "probabilities": probabilities.tolist()
                }
            
            inference_time = time.time() - start_time
            self.inference_stats["cpu_inferences"] += 1
            self.inference_stats["cpu_total_time"] += inference_time
            self.inference_stats["cpu_avg_time"] = (
                self.inference_stats["cpu_total_time"] / self.inference_stats["cpu_inferences"]
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in CPU inference: {e}")
            return None

    def detect_anomalies(self, data_record):
        logger.debug(f"Detecting anomalies for record: {data_record}")
        timestamp = datetime.now().isoformat()
        alert = {
            "original_record": data_record,
            "binary_prediction": None,
            "binary_proba": None,
            "multiclass_prediction": None, 
            "multiclass_proba": None,    
            "timestamp": timestamp,
            "agent_notes": [],
            "inference_mode": "cpu",  # Default to CPU
            "processing_time_ms": 0.0,
            "gpu_accelerated": False
        }
        
        start_time = time.time()
        
        # Determine optimal inference method
        use_gpu = self.cuml_available and self.nvidia_gpu_available
        
        if use_gpu:
            alert["inference_mode"] = "gpu"
            alert["gpu_accelerated"] = True
            logger.debug("Attempting GPU-accelerated inference")
            
            # Try GPU preprocessing and inference
            gpu_features_binary = self._preprocess_for_gpu(data_record, feature_columns_binary, self.gpu_scaler_binary)
            gpu_features_multi = self._preprocess_for_gpu(data_record, feature_columns_multi, self.gpu_scaler_multi)
            
            if gpu_features_binary is not None and gpu_features_multi is not None:
                # Attempt GPU inference (would be used when GPU models are trained)
                gpu_binary_result = self._gpu_inference(gpu_features_binary, "binary")
                
                # For now, fall back to CPU with optimized preprocessing
                if gpu_binary_result is None:
                    logger.debug("GPU inference not available, falling back to CPU with GPU preprocessing")
                    alert["inference_mode"] = "gpu_preprocessing_cpu_inference"
                    
                    # Convert GPU preprocessed data back for CPU inference
                    try:
                        if hasattr(gpu_features_binary, 'get'):
                            # Convert CuPy array to NumPy
                            import cupy as cp
                            scaled_features_binary = cp.asnumpy(gpu_features_binary)
                            scaled_features_multi = cp.asnumpy(gpu_features_multi)
                        else:
                            # Use CPU fallback
                            scaled_features_binary = self.preprocess_input(data_record, feature_columns_binary, scaler=self.scaler_binary)
                            scaled_features_multi = self.preprocess_input(data_record, feature_columns_multi, scaler=self.scaler_multi)
                    except:
                        # Complete fallback to CPU preprocessing
                        scaled_features_binary = self.preprocess_input(data_record, feature_columns_binary, scaler=self.scaler_binary)
                        scaled_features_multi = self.preprocess_input(data_record, feature_columns_multi, scaler=self.scaler_multi)
                else:
                    # Use GPU results (when available)
                    scaled_features_binary = gpu_features_binary
                    scaled_features_multi = gpu_features_multi
            else:
                # Fallback to CPU preprocessing
                logger.debug("GPU preprocessing failed, using CPU preprocessing")
                alert["inference_mode"] = "cpu_fallback"
                alert["gpu_accelerated"] = False
                scaled_features_binary = self.preprocess_input(data_record, feature_columns_binary, scaler=self.scaler_binary)
                scaled_features_multi = self.preprocess_input(data_record, feature_columns_multi, scaler=self.scaler_multi)
        else:
            # Standard CPU processing
            alert["inference_mode"] = "cpu"
            scaled_features_binary = self.preprocess_input(data_record, feature_columns_binary, scaler=self.scaler_binary)
            scaled_features_multi = self.preprocess_input(data_record, feature_columns_multi, scaler=self.scaler_multi)
        
        # Check preprocessing results
        if scaled_features_binary is None:
            alert['error'] = 'Preprocessing(binary) failed due to missing features or other issues.'
            alert['agent_notes'].append('Preprocessing Error')
            alert['timestamp'] = datetime.now().isoformat() 
            logger.error(f"Preprocessing failed for record: {data_record}. Alert: {alert}")
            return alert
        
        if scaled_features_multi is None:
            alert['error'] = 'Preprocessing(multi) failed due to missing features or other issues.'
            alert['agent_notes'].append('Preprocessing Error')
            alert['timestamp'] = datetime.now().isoformat() 
            logger.error(f"Preprocessing failed for record: {data_record}. Alert: {alert}")
            return alert

        # Perform binary classification inference
        if use_gpu and self.gpu_binary_model is not None:
            # GPU inference (when models are trained)
            binary_result = self._gpu_inference(scaled_features_binary, "binary")
            if binary_result is not None:
                alert["binary_proba"] = binary_result["probabilities"]
                alert["binary_prediction"] = binary_result["prediction"]
            else:
                # Fallback to CPU inference
                binary_result = self._cpu_inference(scaled_features_binary, "binary")
                if binary_result is not None:
                    alert["binary_proba"] = binary_result["probabilities"]
                    alert["binary_prediction"] = binary_result["prediction"]
        else:
            # CPU inference
            binary_result = self._cpu_inference(scaled_features_binary, "binary")
            if binary_result is not None:
                alert["binary_proba"] = binary_result["probabilities"]
                alert["binary_prediction"] = binary_result["prediction"]

        # Perform multiclass classification if anomaly detected
        if alert["binary_prediction"] == 1:
            logger.info("Binary prediction is 1 (anomaly), proceeding to multiclass classification.")
            
            if use_gpu and self.gpu_multiclass_model is not None:
                # GPU inference (when models are trained)
                multi_result = self._gpu_inference(scaled_features_multi, "multiclass")
                if multi_result is not None:
                    alert["multiclass_prediction"] = multi_result["prediction_label"]
                    alert["multiclass_proba"] = multi_result["probabilities"]
                else:
                    # Fallback to CPU inference
                    multi_result = self._cpu_inference(scaled_features_multi, "multiclass")
                    if multi_result is not None:
                        alert["multiclass_prediction"] = multi_result["prediction_label"]
                        alert["multiclass_proba"] = multi_result["probabilities"]
            else:
                # CPU inference
                multi_result = self._cpu_inference(scaled_features_multi, "multiclass")
                if multi_result is not None:
                    alert["multiclass_prediction"] = multi_result["prediction_label"]
                    alert["multiclass_proba"] = multi_result["probabilities"]
        
        # Calculate processing time
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        alert["processing_time_ms"] = round(processing_time, 3)
        
        if not alert["agent_notes"]:
            alert["agent_notes"].append("Detection cycle complete.")
            
        # Add performance note
        if alert["gpu_accelerated"]:
            alert["agent_notes"].append(f"GPU-accelerated processing ({alert['inference_mode']})")
        
        logger.debug(f"Generated detection alert: {alert}")
        return alert
    
    def get_performance_stats(self):
        """Get current inference performance statistics."""
        return self.inference_stats.copy()


if __name__ == '__main__':
    # Basic logging setup for direct testing of this script
    # Ensure json is imported for the test dump
    import json
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', stream=sys.stdout)
    logger.info("--- Test Anomaly Detection Agent (Direct Run with Logging) ---")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, '..', '..')) 
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    from continuum5g_insight.agents.data_streaming_agent import DataStreamer

    logger.info(f"IMPORTANT: Ensure model paths (defined in this script) are correct relative to this script's location")
    logger.info(f"           and actual .joblib files exist in 'continuum5g_insight_project/models/rf_models/'.")
    logger.critical("CRITICAL REMINDER: The `feature_columns` list in `AnomalyDetectionAgent.preprocess_input()` MUST be customized.")

    agent = AnomalyDetectionAgent(MODEL_PATH_BINARY, MODEL_PATH_MULTICLASS, MODEL_PATH_BINARY_SCALER, MODEL_PATH_MULTI_SCALER)

    if agent.binary_model is None and agent.multiclass_model is None:
        logger.error("\nOne or more CLASSIFICATION models failed to load. Test cannot proceed effectively.")
    elif agent.scaler_binary is None or agent.scaler_multi is None: # Corrected scaler check
         logger.error("\nOne or both StandardScalers failed to load. Predictions will be incorrect. Test cannot proceed effectively.")
    else:
        DATASET_FILE_PATH = "../../data/sample_inputs/5g_nidd_dataset.csv.placeholder" 
        abs_dataset_path = os.path.abspath(os.path.join(current_dir, DATASET_FILE_PATH)) 
        
        logger.info(f"\nStreaming data from: '{abs_dataset_path}' for detection...")
        
        streamer = DataStreamer(csv_path=abs_dataset_path, default_frequency_hz=100)
        if streamer.df is None or streamer.df.empty:
            logger.error("Test DataStreamer failed to load data. Cannot proceed with detection test.")
        else:
            sample_test_record = {f'feature_{i}': float(i) for i in range(1, 21)}
            logger.info(f"\nUsing a synthetic sample record for testing `detect_anomalies`: {sample_test_record}")
            
            alert = agent.detect_anomalies(sample_test_record)
            logger.info(f"\n  Alert Generated for Synthetic Record: {json.dumps(alert, indent=2, default=str)}")

            logger.info("\n--- Testing with records from streamer (will likely show feature mismatch if defaults are used) ---")
            record_count = 0
            detections_made = 0
            max_records_to_process = 3

            for record in streamer.send_n_records(max_records_to_process): 
                if not record: 
                    continue
                
                logger.info(f"\nProcessing record {record_count + 1} from stream: {record}")
                alert = agent.detect_anomalies(record)
                logger.info(f"  Alert Generated from Stream: {json.dumps(alert, indent=2, default=str)}")
                
                record_count += 1
                if alert.get("binary_prediction") == 1 or alert.get("multiclass_prediction") not in [None, 'Benign', 'Unknown_Label_Index']:
                    detections_made +=1
                    logger.info(f"  Anomaly detected for record {record_count}.")

            logger.info(f"\n--- Finished Detection Test ---")
            logger.info(f"Processed {record_count} records from stream.")
            logger.info(f"Detected anomalies in {detections_made} records from stream.")
            
            if record_count == 0 and not (streamer.df is None or streamer.df.empty):
                 logger.warning("No records were processed from the streamer, though streamer had data. Check stream logic or feature issues.")
            elif streamer.df is None or streamer.df.empty:
                 logger.error("No records were streamed from the dataset because the streamer's DataFrame was empty.")

        if hasattr(agent, '_preprocess_warning_shown') or hasattr(agent, '_placeholder_feature_warning_shown'):
            logger.warning("\nREMINDER: `feature_columns` in `preprocess_input` MUST be customized for correct model performance.")
        if (agent.scaler_binary is None or agent.scaler_multi is None) and hasattr(agent, '_scaler_missing_warning_shown'): # Corrected check
             pass 
        elif agent.scaler_binary is None or agent.scaler_multi is None: # Corrected check
            logger.warning("REMINDER: One or both StandardScalers were NOT loaded. Predictions made are on unscaled data.")
    logger.info("--- Finished Anomaly Detection Agent Test (Direct Run) ---")

