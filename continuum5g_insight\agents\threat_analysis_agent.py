# Continuum5G Insight - Threat Analysis & Correlation Agent
# Uses NeMo SLM with RAG to analyze and correlate threats.

import json
import os
import datetime 
import time     
import logging
import sys # Add this import

logger = logging.getLogger(f"continuum5g_insight.agents.{__name__}")

# --- IMPORTANT USER REMINDER ---
# (User reminders via print are kept for direct script execution visibility)
print("------------------------------------------------------------------------------------")
print("IMPORTANT KNOWLEDGE BASE PATH CONFIGURATION (ThreatAnalysisAgent):")
print("The KB_PATH_THREAT_ACTORS path below is a placeholder.")
print("It's currently set relative to this agent's file location for initial setup.")
KB_PATH_THREAT_ACTORS = "../knowledge_bases/threat_actor_kb.json" # Relative to agents/
# Resolve and print absolute path for clarity during direct execution or logging
abs_kb_path = os.path.abspath(os.path.join(os.path.dirname(__file__), KB_PATH_THREAT_ACTORS))
print(f"Threat Actors KB Path (resolved by ThreatAnalysisAgent): {abs_kb_path}")
print("------------------------------------------------------------------------------------")


def load_knowledge_base(kb_path):
    """
    Loads a knowledge base from the given JSON file path.
    Path is assumed to be relative to this script's directory if not absolute.
    """
    script_dir = os.path.dirname(__file__)
    resolved_kb_path = os.path.abspath(os.path.join(script_dir, kb_path))
    logger.info(f"Attempting to load knowledge base from resolved path: {resolved_kb_path}")

    if not os.path.exists(resolved_kb_path):
        logger.error(f"Knowledge base file not found at {resolved_kb_path}.")
        return {}

    try:
        with open(resolved_kb_path, 'r') as f:
            kb = json.load(f)
        logger.info(f"Successfully loaded knowledge base from: {resolved_kb_path}. Contains {len(kb)} entries.")
        return kb
    except json.JSONDecodeError as e:
        logger.error(f"Could not decode JSON from knowledge base file {resolved_kb_path}. Error: {e}", exc_info=True)
        return {}
    except Exception as e:
        logger.error(f"An unexpected error occurred while loading KB {resolved_kb_path}: {e}", exc_info=True)
        return {}

def simulate_slm_call(prompt, service_name="ThreatAnalysis", knowledge_base=None, alert_data=None):
    """
    Simulates a call to an SLM (e.g., served via NVIDIA NIM) for threat analysis.
    Now incorporates basic RAG by looking up info in the knowledge_base based on alert_data.
    """
    logger.info(f"--- SIMULATING SLM Call for {service_name} ---")
    logger.debug(f"PROMPT for {service_name} SLM:\\n{prompt}\\n--- END PROMPT ---")
    
    response = {
        "analysis_summary": "No specific analysis generated by SLM.",
        "priority_assessment": "Not assessed",
        "correlation_notes": [],
        "rag_references": []
    }

    if knowledge_base is None:
        knowledge_base = {}
    if alert_data is None:
        alert_data = {}

    multiclass_pred = alert_data.get("multiclass_prediction", "N/A")
    binary_pred = alert_data.get("binary_prediction")
    original_record = alert_data.get("original_record", {})

    # Basic RAG: Check against alerts_enrichment rules in KB
    for rule in knowledge_base.get("alerts_enrichment", []):
        applies = False
        if "if_alert_contains_multiclass" in rule and multiclass_pred in rule["if_alert_contains_multiclass"]:
            applies = True
        elif "if_alert_is_binary_anomaly_only" in rule and rule["if_alert_is_binary_anomaly_only"] and binary_pred == 1 and (multiclass_pred == "N/A" or multiclass_pred is None or multiclass_pred == CLASS_LABELS_MAP.get(0)): # Assuming 0 is Benign
            applies = True
            
        if applies:
            response["analysis_summary"] = rule.get("add_context_summary", response["analysis_summary"])
            response["priority_assessment"] = rule.get("initial_priority_assessment", response["priority_assessment"])
            response["correlation_notes"].append(f"Rule matched: Based on detection of '{multiclass_pred if multiclass_pred != 'N/A' else 'binary anomaly'}'. Intent: {rule.get('possible_intent', 'N/A')}")
            response["rag_references"].append({"source": "KB:alerts_enrichment", "rule_condition": str(rule.get("if_alert_contains_multiclass") or rule.get("if_alert_is_binary_anomaly_only"))})
            break # First matching rule applies for this simulation

    # Basic RAG: Check against known_iocs (example: source IP)
    # Ensure 'srcip' or the correct source IP field name is used based on your actual data_record structure.
    # If your data_record uses a different key for source IP (e.g., 'saddr', 'source_ip'), update it here.
    src_ip_field_in_record = 'srcip' # COMMON FIELD, BUT VERIFY FROM YOUR DATASET LOGS
    src_ip = original_record.get(src_ip_field_in_record) 
    
    if src_ip:
        for ioc in knowledge_base.get("known_iocs", []):
            if ioc.get("type") == "ip" and ioc.get("value") == src_ip:
                response["correlation_notes"].append(f"IOC Match: Source IP {src_ip} is a known IOC. Description: {ioc.get('description')}")
                response["priority_assessment"] = "High" # Elevate priority if IOC matches
                response["rag_references"].append({"source": "KB:known_iocs", "ioc_value": src_ip})
                break
                
    # Simulate SLM processing time
    time.sleep(0.05) 
    
    logger.debug(f"SIMULATED SLM RESPONSE for {service_name}:\\n{json.dumps(response, indent=2)}\\n--- END SIMULATED SLM Call ---")
    return response


class ThreatAnalysisAgent:
    def __init__(self, kb_path=KB_PATH_THREAT_ACTORS):
        logger.info("Initializing ThreatAnalysisAgent...")
        self.knowledge_base = load_knowledge_base(kb_path)
        if not self.knowledge_base:
            logger.warning(f"Threat actor knowledge base failed to load from {kb_path} or is empty. RAG capabilities will be severely limited.")
        else:
            logger.info(f"Threat actor knowledge base loaded successfully with {len(self.knowledge_base.get('alerts_enrichment',[]))} enrichment rules, {len(self.knowledge_base.get('device_profiles',[]))} device profiles, {len(self.knowledge_base.get('known_iocs',[]))} IOCs.")
        # Placeholder for NeMo/NIM client if we were to make actual calls
        # self.nemo_client = SomeNemoNIMClient() 
        logger.info("ThreatAnalysisAgent initialized.")

    def analyze_threat(self, detection_alert):
        """
        Analyzes a threat alert using simulated SLM call with RAG.
        detection_alert is the output from AnomalyDetectionAgent.
        """
        logger.debug(f"Analyzing threat for alert: {detection_alert}")
        
        # Construct a prompt for the SLM (even if simulated, good practice)
        prompt = self._build_slm_prompt(detection_alert)

        # Simulate SLM call, passing the KB and the raw alert for RAG
        slm_analysis_result = simulate_slm_call(
            prompt, 
            service_name="ThreatAnalysisRAG",
            knowledge_base=self.knowledge_base,
            alert_data=detection_alert 
        )

        enriched_alert = {
            "original_detection": detection_alert,
            "slm_analysis": slm_analysis_result,
            "timestamp": datetime.datetime.now().isoformat(), 
            "agent_notes": ["Threat analysis complete using SLM+RAG simulation."]
        }
        
        # Merge SLM assessment into the top level for easier access by response agent
        enriched_alert["analysis_summary"] = slm_analysis_result.get("analysis_summary")
        enriched_alert["priority_assessment"] = slm_analysis_result.get("priority_assessment")
        enriched_alert["multiclass_prediction"] = detection_alert.get("multiclass_prediction") 
        enriched_alert["binary_prediction"] = detection_alert.get("binary_prediction") 
        enriched_alert["original_record"] = detection_alert.get("original_record") 

        logger.info(f"Threat analysis generated: {enriched_alert['analysis_summary']} (Priority: {enriched_alert['priority_assessment']})")
        return enriched_alert

    def _build_slm_prompt(self, detection_alert):
        """
        Helper function to build a detailed prompt for the SLM.
        """
        try:
            alert_details = json.dumps(detection_alert, default=str) 
        except Exception as e:
            logger.error(f"Error serializing detection_alert for prompt: {e}")
            alert_details = str(detection_alert) 

        prompt = f"""Analyze the following 5G network security alert for potential threats, assess its priority, and provide a contextual summary.
        Consider information from our knowledge base regarding known IOCs, device profiles, and typical alert patterns.

        Alert Data:
        {alert_details}

        Focus on:
        1. Summarizing the nature of the event.
        2. Assessing the threat priority (Low, Medium, High, Critical).
        3. Correlating with any known IOCs or device vulnerabilities if applicable from the context.
        4. Referencing any RAG sources used for the analysis.

        Provide your analysis in JSON format with keys: "analysis_summary", "priority_assessment", "correlation_notes", "rag_references".
        """
        return prompt

# (Ensure CLASS_LABELS_MAP is accessible if used by simulate_slm_call indirectly, or pass necessary info)
# It's better if CLASS_LABELS_MAP is defined globally or passed around if needed by helper functions.
# For now, assuming simulate_slm_call's logic is self-contained or gets what it needs from alert_data.
# Adding CLASS_LABELS_MAP here for completeness if it's needed by the modified simulate_slm_call
CLASS_LABELS_MAP = {
    0: 'Benign', 1: 'UDPFlood', 2: 'HTTPFlood', 3: 'SlowrateDoS',
    4: 'TCPConnectScan', 5: 'SYNScan', 6: 'UDPScan',
    7: 'SYNFlood', 8: 'ICMPFlood'
}

if __name__ == '__main__':
    import logging # Ensure logging is imported for direct run
    import os # Ensure os is imported
    # Basic logging setup for direct testing of this script
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', stream=sys.stdout)
    logger.info("--- Test ThreatAnalysisAgent (Direct Run with Logging) ---")
    
    # Example Test Data (adjust fields based on actual AnomalyDetectionAgent output)
    sample_alert_from_detection = {
        'original_record': {'src_ip': '********', 'dst_ip': '*************', 'dst_port': 80, 'protocol': 'TCP', 'flow_duration': 60, 'byte_count': 1500},
        'binary_prediction': 1,
        'binary_proba': [0.1, 0.9],
        'multiclass_prediction': 'PortScan', 
        'multiclass_proba': [0.1, 0.05, 0.8, 0.05], 
        'timestamp': datetime.datetime.now().isoformat(),
        'agent_notes': 'Detection successful.'
    }
    
    sample_alert_ddos = {
        'original_record': {'src_ip': '*******', 'dst_ip': '*************', 'dst_port': 53, 'protocol': 'UDP', 'packet_count': 10000, 'byte_count': 5000000},
        'binary_prediction': 1,
        'binary_proba': [0.05, 0.95],
        'multiclass_prediction': 'DDoS_Attempt', 
        'multiclass_proba': [0.92, 0.03, 0.05],
        'timestamp': datetime.datetime.now().isoformat(),
        'agent_notes': 'Detection successful, high volume UDP traffic.'
    }
    
    kb_file_path_for_test = KB_PATH_THREAT_ACTORS 
    
    test_kb_content = {
        "PortScan": {
            "description": "An attempt to discover open ports on a target system. Often uses TCP or UDP.",
            "typical_actors": ["Automated scanners", "Reconnaissance phase of APTs"],
            "common_ports_targeted": ["21", "22", "23", "25", "80", "443", "3389", "8080"],
            "mitigation_basic": "Ensure firewalls block unnecessary ports. Monitor for excessive connection attempts.",
            "severity_estimate_basic": "Medium"
        },
        "DDoS_Attempt": {
            "description": "A Distributed Denial of Service attack characterized by overwhelming traffic.",
            "typical_vectors": ["UDP flood", "SYN flood", "ICMP flood", "HTTP flood"],
            "mitigation_basic": "Use traffic scrubbing services, rate limiting, null routing.",
            "severity_estimate_basic": "High"
        }
    }
    
    script_dir_for_test = os.path.dirname(__file__)
    resolved_kb_file_path_for_test = os.path.abspath(os.path.join(script_dir_for_test, kb_file_path_for_test))
    kb_dir_for_test = os.path.dirname(resolved_kb_file_path_for_test)

    if not os.path.exists(kb_dir_for_test):
        try:
            os.makedirs(kb_dir_for_test)
            logger.info(f"Test: Created directory for KB: {kb_dir_for_test}")
        except OSError as e:
            logger.error(f"Test: Error creating directory for KB '{kb_dir_for_test}': {e}")

    if os.path.exists(kb_dir_for_test): 
        try:
            with open(resolved_kb_file_path_for_test, 'w') as f:
                json.dump(test_kb_content, f, indent=4)
            logger.info(f"Test: Populated placeholder KB at '{resolved_kb_file_path_for_test}' for testing.")
        except IOError as e:
            logger.error(f"Test: Error writing placeholder KB for test at '{resolved_kb_file_path_for_test}': {e}")
    else:
        logger.warning(f"Test: Skipping KB population as directory '{kb_dir_for_test}' could not be confirmed.")

    agent = ThreatAnalysisAgent(kb_path_threat_actors=KB_PATH_THREAT_ACTORS) 

    if not agent.threat_actors_kb:
        logger.warning("Test: Threat actors KB is empty or failed to load. RAG context in SLM prompts will be limited for the test.")

    logger.info("\n--- Analyzing 'PortScan' Alert with Simulated SLM ---")
    analysis_result_1 = agent.analyze_alert(sample_alert_from_detection)
    logger.info(f"\nFinal Analysis Result 1: {json.dumps(analysis_result_1, indent=2, default=str)}")

    logger.info("\n--- Analyzing 'DDoS_Attempt' Alert with Simulated SLM ---")
    analysis_result_ddos = agent.analyze_alert(sample_alert_ddos)
    logger.info(f"\nFinal Analysis Result DDoS: {json.dumps(analysis_result_ddos, indent=2, default=str)}")
    
    logger.info("\n--- Finished Threat Analysis Agent Test (Direct Run with Logging, Simulated SLM) ---")

    try:
        if os.path.exists(resolved_kb_file_path_for_test):
            logger.info(f"Test KB file at '{resolved_kb_file_path_for_test}' can be manually removed if desired.")
    except Exception as e:
        logger.error(f"Error cleaning up test KB file: {e}", exc_info=True)

