"""
Gradio Control Panel for 5G Security Analytics
"""

import gradio as gr
import json
import os
import sys
import time
import threading
import logging
from pathlib import Path
from typing import Dict, Any, Tuple

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.data_loader import DataLoader
from src.core.batch_processor import BatchProcessor
from src.models.anomaly_detector import AnomalyDetector

logger = logging.getLogger(__name__)

# Shared state file
STATE_FILE = "processing_state.json"

def load_state():
    """Load processing state from file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except:
            pass
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

def save_state(state):
    """Save processing state to file"""
    try:
        with open(STATE_FILE, 'w') as f:
            json.dump(state, f)
    except Exception as e:
        logger.error(f"Error saving state: {e}")

def add_log(message: str, level: str = "INFO"):
    """Add log message to state"""
    state = load_state()
    timestamp = time.strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {level}: {message}"
    state["log_messages"].append(log_entry)
    
    # Keep only last 100 messages
    if len(state["log_messages"]) > 100:
        state["log_messages"] = state["log_messages"][-100:]
    
    save_state(state)
    logger.info(message)

class ControlPanel:
    """
    Gradio control panel for managing processing
    """
    
    def __init__(self):
        """Initialize control panel"""
        self.data_loader = None
        self.detector = None
        self.batch_processor = None
        self.processing_thread = None
        
        # Initialize components
        self.initialize_components()
    
    def initialize_components(self):
        """Initialize data loader, detector, and batch processor"""
        try:
            # Initialize data loader
            if os.path.exists("data/sample_inputs/5g_nidd_dataset.csv"):
                self.data_loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
                add_log("Data loader initialized successfully")
            else:
                add_log("Dataset not found", "ERROR")
            
            # Initialize detector
            if os.path.exists("models/rf_models"):
                self.detector = AnomalyDetector("models/rf_models")
                if self.detector.binary_model is not None:
                    self.batch_processor = BatchProcessor(self.detector)
                    add_log("Models loaded successfully")
                else:
                    add_log("Models failed to load", "ERROR")
            else:
                add_log("Models directory not found", "ERROR")
                
        except Exception as e:
            add_log(f"Error initializing components: {e}", "ERROR")
    
    def get_system_status(self) -> Tuple[str, str, str]:
        """Get current system status"""
        # Dataset status
        if self.data_loader and self.data_loader.df is not None:
            dataset_info = self.data_loader.get_dataset_info()
            dataset_status = f"✅ Dataset loaded: {dataset_info['total_records']:,} records"
        else:
            dataset_status = "❌ Dataset not loaded"
        
        # Model status
        if self.detector and self.detector.binary_model is not None:
            model_status = "✅ Models loaded successfully"
        else:
            model_status = "❌ Models not loaded"
        
        # Processing status
        state = load_state()
        if state["is_processing"]:
            processing_status = f"🔄 Processing... Batch {state['processing_stats']['current_batch']}"
        else:
            processing_status = "⏸️ Ready to process"
        
        return dataset_status, model_status, processing_status
    
    def start_processing(self, batch_size: int, max_batches: int,
                        requests_per_second: float, processing_mode: str) -> Tuple[str, str, str, str]:
        """Start batch processing"""
        if not self.data_loader or not self.batch_processor:
            add_log("Cannot start processing: components not initialized", "ERROR")
            return self.get_system_status() + ("❌ Cannot start: components not ready",)

        state = load_state()
        if state["is_processing"]:
            add_log("Processing already running", "WARNING")
            return self.get_system_status() + ("⚠️ Already processing",)

        # Reset data loader position
        self.data_loader.reset_stream()

        # Clear previous results
        state.update({
            "is_processing": True,
            "batch_size": batch_size,
            "max_batches": max_batches,
            "requests_per_second": requests_per_second,
            "processing_mode": processing_mode,
            "current_results": [],  # Clear previous results
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            }
        })
        save_state(state)

        add_log(f"Starting processing: {batch_size} records/batch, {max_batches} batches max")

        # Start processing in background thread
        self.processing_thread = threading.Thread(
            target=self._process_batches,
            args=(batch_size, max_batches, requests_per_second),
            daemon=True
        )
        self.processing_thread.start()

        add_log("Processing thread started successfully")
        return self.get_system_status() + ("🚀 Processing started",)
    
    def stop_processing(self) -> Tuple[str, str, str, str]:
        """Stop processing"""
        state = load_state()
        state["is_processing"] = False
        save_state(state)
        
        add_log("Processing stopped by user")
        return self.get_system_status() + ("⏹️ Processing stopped",)
    
    def _process_batches(self, batch_size: int, max_batches: int, requests_per_second: float):
        """Process batches in background thread"""
        try:
            inter_record_delay = 1.0 / requests_per_second if requests_per_second > 0 else 0.01
            
            for batch_num in range(max_batches):
                state = load_state()
                if not state["is_processing"]:
                    break
                
                # Get batch data
                batch_df = self.data_loader.get_batch(batch_size)
                if batch_df is None or batch_df.empty:
                    add_log("No more data to process")
                    break
                
                # Update current batch
                state["processing_stats"]["current_batch"] = batch_num + 1
                save_state(state)
                
                # Process batch
                batch_data = batch_df.to_dict('records')
                start_time = time.time()
                
                batch_results = self.batch_processor.process_batch_sequential(batch_data)
                
                processing_time = time.time() - start_time
                
                # Update results and stats
                state["current_results"].extend(batch_results)
                
                # Keep only last 1000 results for memory efficiency
                if len(state["current_results"]) > 1000:
                    state["current_results"] = state["current_results"][-1000:]
                
                anomaly_count = sum(1 for r in batch_results if r.get("is_anomaly", False))
                
                state["processing_stats"]["total_processed"] += len(batch_results)
                state["processing_stats"]["total_anomalies"] += anomaly_count
                state["processing_stats"]["processing_rate"] = len(batch_results) / processing_time if processing_time > 0 else 0
                
                save_state(state)
                
                add_log(f"Batch {batch_num + 1} completed: {len(batch_results)} records, {anomaly_count} anomalies")
                
                # Simulate delay between batches
                time.sleep(inter_record_delay * len(batch_results))
            
            # Mark processing as complete
            state = load_state()
            state["is_processing"] = False
            save_state(state)
            
            add_log(f"Processing completed: {state['processing_stats']['total_processed']} total records")
            
        except Exception as e:
            add_log(f"Error during processing: {e}", "ERROR")
            state = load_state()
            state["is_processing"] = False
            save_state(state)
    
    def clear_results(self) -> Tuple[str, str, str, str]:
        """Clear all results and reset stats"""
        state = load_state()
        state.update({
            "current_results": [],
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            },
            "log_messages": []
        })
        save_state(state)
        
        add_log("Results and statistics cleared")
        return self.get_system_status() + ("🧹 Results cleared",)
    
    def get_current_stats(self) -> str:
        """Get current processing statistics"""
        state = load_state()
        stats = state.get("processing_stats", {})
        
        return f"""Current Statistics:
• Total Processed: {stats.get('total_processed', 0):,} records
• Total Anomalies: {stats.get('total_anomalies', 0):,}
• Current Batch: {stats.get('current_batch', 0)}
• Processing Rate: {stats.get('processing_rate', 0):.1f} records/sec
• Anomaly Rate: {(stats.get('total_anomalies', 0) / max(stats.get('total_processed', 1), 1) * 100):.1f}%
• Status: {'🔄 Processing' if load_state()['is_processing'] else '⏸️ Ready'}"""


def create_control_panel() -> gr.Blocks:
    """Create the Gradio control panel interface"""

    control_panel = ControlPanel()

    # Advanced CSS for ultra-professional look
    custom_css = """
    /* Global Styling */
    .gradio-container {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    /* Main Container */
    .contain {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        margin: 20px;
        padding: 30px;
    }

    /* Button Styling */
    .gr-button {
        border-radius: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1.2px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: none;
        padding: 12px 24px;
        font-size: 14px;
    }

    .gr-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .gr-button-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .gr-button-secondary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .gr-button-stop {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    /* Input Styling */
    .gr-textbox, .gr-slider {
        border-radius: 10px;
        border: 2px solid #e1e5e9;
        transition: all 0.3s ease;
    }

    .gr-textbox:focus, .gr-slider:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Card Styling */
    .gr-box {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 20px;
        margin: 10px 0;
    }

    /* Header Styling */
    h1, h2, h3 {
        color: #2d3748;
        font-weight: 700;
    }

    /* Status Indicators */
    .status-success {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
    }

    .status-warning {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
    }

    .status-error {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
    }

    /* Animation */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .contain {
            margin: 10px;
            padding: 20px;
        }
    }
    """

    with gr.Blocks(
        title="5G Security Analytics - Professional Control Panel",
        theme=gr.themes.Soft(),
        css=custom_css
    ) as demo:
        # Ultra-professional header with advanced styling
        gr.HTML("""
        <div style="
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');
                opacity: 0.3;
            "></div>
            <div style="position: relative; z-index: 1;">
                <h1 style="
                    color: white;
                    margin: 0;
                    font-size: 3.2em;
                    font-weight: 800;
                    text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
                    letter-spacing: -1px;
                    background: linear-gradient(45deg, #ffffff, #f0f0f0);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                ">
                    🛡️ 5G SECURITY ANALYTICS
                </h1>
                <h2 style="
                    color: rgba(255,255,255,0.9);
                    margin: 15px 0 0 0;
                    font-size: 1.4em;
                    font-weight: 400;
                    text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
                    letter-spacing: 2px;
                ">
                    PROFESSIONAL CONTROL CENTER
                </h2>
                <div style="
                    margin-top: 20px;
                    padding: 10px 20px;
                    background: rgba(255,255,255,0.2);
                    border-radius: 25px;
                    display: inline-block;
                    backdrop-filter: blur(10px);
                ">
                    <span style="
                        color: white;
                        font-size: 0.9em;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">
                        🚀 ENTERPRISE GRADE THREAT DETECTION
                    </span>
                </div>
            </div>
        </div>
        """)

        gr.HTML("""
        <div style="
            text-align: center;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border: 1px solid #dee2e6;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 25px;
        ">
            <div style="
                display: inline-flex;
                align-items: center;
                gap: 15px;
                background: white;
                padding: 15px 25px;
                border-radius: 50px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            ">
                <span style="font-size: 1.5em;">🎯</span>
                <div style="text-align: left;">
                    <div style="
                        font-size: 1.2em;
                        font-weight: 700;
                        color: #2d3748;
                        margin-bottom: 5px;
                    ">
                        Advanced Network Security Platform
                    </div>
                    <div style="
                        font-size: 0.95em;
                        color: #718096;
                        font-weight: 500;
                    ">
                        Configure parameters • Monitor performance • Control threat detection
                    </div>
                </div>
            </div>
        </div>
        """)

        # Ultra-professional System Status Section
        gr.HTML("""
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: -50%;
                right: -50%;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            "></div>
            <div style="position: relative; z-index: 1;">
                <div style="text-align: center;">
                    <div style="
                        display: inline-flex;
                        align-items: center;
                        gap: 12px;
                        background: rgba(255,255,255,0.15);
                        padding: 12px 24px;
                        border-radius: 50px;
                        backdrop-filter: blur(10px);
                    ">
                        <span style="font-size: 1.8em;">📊</span>
                        <h3 style="
                            color: white;
                            margin: 0;
                            font-size: 1.4em;
                            font-weight: 700;
                            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
                            letter-spacing: 1px;
                        ">
                            SYSTEM STATUS & HEALTH MONITOR
                        </h3>
                    </div>
                </div>
            </div>
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("""
                <div style="
                    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                    padding: 20px;
                    border-radius: 15px;
                    border-left: 5px solid #2196f3;
                    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.15);
                    margin-bottom: 15px;
                ">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <span style="font-size: 1.5em;">💾</span>
                        <h4 style="margin: 0; color: #1976d2; font-weight: 700;">Data & Models</h4>
                    </div>
                    <p style="margin: 0; color: #424242; font-size: 0.9em; line-height: 1.4;">
                        Monitor dataset loading and model initialization status
                    </p>
                </div>
                """)
                dataset_status = gr.Textbox(
                    label="📁 Dataset Status",
                    interactive=False,
                    container=True,
                    show_label=True,
                    elem_classes=["status-textbox"]
                )
                model_status = gr.Textbox(
                    label="🤖 Model Status",
                    interactive=False,
                    container=True,
                    show_label=True,
                    elem_classes=["status-textbox"]
                )
            with gr.Column(scale=1):
                gr.HTML("""
                <div style="
                    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
                    padding: 20px;
                    border-radius: 15px;
                    border-left: 5px solid #9c27b0;
                    box-shadow: 0 8px 25px rgba(156, 39, 176, 0.15);
                    margin-bottom: 15px;
                ">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <span style="font-size: 1.5em;">⚙️</span>
                        <h4 style="margin: 0; color: #7b1fa2; font-weight: 700;">Operations</h4>
                    </div>
                    <p style="margin: 0; color: #424242; font-size: 0.9em; line-height: 1.4;">
                        Track processing status and recent operations
                    </p>
                </div>
                """)
                processing_status = gr.Textbox(
                    label="⚙️ Processing Status",
                    interactive=False,
                    container=True,
                    show_label=True,
                    elem_classes=["status-textbox"]
                )
                operation_status = gr.Textbox(
                    label="📋 Last Operation",
                    interactive=False,
                    container=True,
                    show_label=True,
                    elem_classes=["status-textbox"]
                )

        # Ultra-professional Configuration Section
        gr.HTML("""
        <div style="
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.3);
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: -30%;
                left: -30%;
                width: 60%;
                height: 60%;
                background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
            "></div>
            <div style="position: relative; z-index: 1;">
                <div style="text-align: center;">
                    <div style="
                        display: inline-flex;
                        align-items: center;
                        gap: 15px;
                        background: rgba(255,255,255,0.2);
                        padding: 15px 30px;
                        border-radius: 50px;
                        backdrop-filter: blur(15px);
                    ">
                        <span style="font-size: 2em;">⚙️</span>
                        <h3 style="
                            color: white;
                            margin: 0;
                            font-size: 1.5em;
                            font-weight: 700;
                            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
                            letter-spacing: 1px;
                        ">
                            ADVANCED PROCESSING CONFIGURATION
                        </h3>
                    </div>
                </div>
            </div>
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("""
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; border-left: 4px solid #2196f3;">
                    <h4 style="margin: 0 0 10px 0; color: #1976d2;">📊 Batch Processing Settings</h4>
                    <p style="margin: 0; color: #424242; font-size: 0.9em;">Configure how data is processed in batches</p>
                </div>
                """)
                batch_size = gr.Slider(
                    minimum=10, maximum=1000, value=100, step=10,
                    label="📦 Batch Size",
                    info="Number of records to process per batch (10-1000)"
                )
                max_batches = gr.Slider(
                    minimum=1, maximum=100, value=100, step=1,
                    label="🔢 Maximum Batches",
                    info="Maximum number of batches to process (1-100)"
                )

            with gr.Column(scale=1):
                gr.Markdown("""
                <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; border-left: 4px solid #9c27b0;">
                    <h4 style="margin: 0 0 10px 0; color: #7b1fa2;">⚡ Performance Settings</h4>
                    <p style="margin: 0; color: #424242; font-size: 0.9em;">Control processing speed and mode</p>
                </div>
                """)
                requests_per_second = gr.Slider(
                    minimum=0.1, maximum=100.0, value=10.0, step=0.1,
                    label="⚡ Requests per Second",
                    info="Processing rate (0.1-100.0 records per second)"
                )
                processing_mode = gr.Dropdown(
                    choices=["Manual Batch", "Continuous Stream"],
                    value="Manual Batch",
                    label="🔄 Processing Mode",
                    info="Select processing mode for data analysis"
                )

        # Ultra-professional Control Center
        gr.HTML("""
        <div style="
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            padding: 25px;
            border-radius: 20px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(250, 112, 154, 0.3);
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 60 60\"><defs><pattern id=\"hexagon\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"><polygon points=\"30,5 50,20 50,40 30,55 10,40 10,20\" fill=\"none\" stroke=\"%23ffffff\" stroke-width=\"1\" opacity=\"0.1\"/></pattern></defs><rect width=\"100%\" height=\"100%\" fill=\"url(%23hexagon)\"/></svg>');
            "></div>
            <div style="position: relative; z-index: 1;">
                <div style="text-align: center;">
                    <div style="
                        display: inline-flex;
                        align-items: center;
                        gap: 15px;
                        background: rgba(255,255,255,0.25);
                        padding: 18px 35px;
                        border-radius: 50px;
                        backdrop-filter: blur(20px);
                        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    ">
                        <span style="font-size: 2.2em;">🎮</span>
                        <h3 style="
                            color: white;
                            margin: 0;
                            font-size: 1.6em;
                            font-weight: 800;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
                            letter-spacing: 1.5px;
                        ">
                            MISSION CONTROL CENTER
                        </h3>
                    </div>
                    <div style="
                        margin-top: 15px;
                        color: rgba(255,255,255,0.9);
                        font-size: 1em;
                        font-weight: 500;
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                    ">
                        Execute critical system operations with precision
                    </div>
                </div>
            </div>
        </div>
        """)

        # Professional button grid with enhanced styling
        gr.HTML("""
        <div style="
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 25px 0;
        ">
        </div>
        """)

        with gr.Row():
            with gr.Column(scale=1):
                start_btn = gr.Button(
                    "🚀 INITIATE PROCESSING",
                    variant="primary",
                    size="lg",
                    elem_classes=["gr-button-primary"],
                    scale=1
                )
            with gr.Column(scale=1):
                stop_btn = gr.Button(
                    "⏹️ TERMINATE PROCESSING",
                    variant="stop",
                    size="lg",
                    elem_classes=["gr-button-stop"],
                    scale=1
                )

        with gr.Row():
            with gr.Column(scale=1):
                clear_btn = gr.Button(
                    "🧹 PURGE RESULTS",
                    variant="secondary",
                    size="lg",
                    elem_classes=["gr-button-secondary"],
                    scale=1
                )
            with gr.Column(scale=1):
                refresh_btn = gr.Button(
                    "🔄 REFRESH SYSTEM",
                    size="lg",
                    elem_classes=["gr-button-secondary"],
                    scale=1
                )

        # Ultra-professional Statistics Section
        gr.HTML("""
        <div style="
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 20px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(168, 237, 234, 0.3);
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: -20%;
                right: -20%;
                width: 40%;
                height: 40%;
                background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            "></div>
            <div style="position: relative; z-index: 1;">
                <div style="text-align: center;">
                    <div style="
                        display: inline-flex;
                        align-items: center;
                        gap: 15px;
                        background: rgba(255,255,255,0.4);
                        padding: 18px 35px;
                        border-radius: 50px;
                        backdrop-filter: blur(15px);
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    ">
                        <span style="font-size: 2em;">📈</span>
                        <h3 style="
                            color: #2c3e50;
                            margin: 0;
                            font-size: 1.5em;
                            font-weight: 800;
                            letter-spacing: 1px;
                        ">
                            REAL-TIME PERFORMANCE ANALYTICS
                        </h3>
                    </div>
                    <div style="
                        margin-top: 12px;
                        color: #4a5568;
                        font-size: 1em;
                        font-weight: 600;
                    ">
                        Live monitoring of system performance metrics
                    </div>
                </div>
            </div>
        </div>
        """)

        stats_display = gr.Textbox(
            label="📊 Live Processing Statistics & Performance Metrics",
            lines=10,
            interactive=False,
            container=True,
            show_label=True,
            elem_classes=["stats-display"]
        )

        # Information Section with professional styling
        with gr.Accordion("📚 Professional User Guide & System Information", open=False):
            gr.HTML("""
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; color: white;">
                <h3 style="margin: 0 0 15px 0; text-align: center;">🎯 Professional Control Panel Guide</h3>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #f0f0f0;">🚀 Quick Start Workflow</h4>
                    <ol style="margin: 0; padding-left: 20px; color: #e0e0e0;">
                        <li><strong>System Health Check</strong>: Verify dataset and models are loaded</li>
                        <li><strong>Configure Parameters</strong>: Set batch size (10-1000), processing rate, and mode</li>
                        <li><strong>Initiate Processing</strong>: Click START PROCESSING to begin threat analysis</li>
                        <li><strong>Real-time Monitoring</strong>: Use Streamlit dashboard for live visualization</li>
                        <li><strong>Control Operations</strong>: Stop, clear, or refresh as needed</li>
                    </ol>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #f0f0f0;">⚙️ Advanced Configuration</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #e0e0e0;">
                        <li><strong>Batch Size (10-1000)</strong>: Higher values = better throughput, more memory usage</li>
                        <li><strong>Max Batches (1-100)</strong>: Prevents system overload, controls total processing</li>
                        <li><strong>Requests/Second (0.1-100)</strong>: Fine-tune processing speed for optimal performance</li>
                        <li><strong>Processing Mode</strong>: Manual for controlled analysis, Continuous for streaming</li>
                    </ul>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0 0 10px 0; color: #f0f0f0;">📊 Dashboard Integration</h4>
                    <p style="margin: 0; color: #e0e0e0;">
                        Access the professional Streamlit dashboard for comprehensive real-time monitoring:<br>
                        <code style="background: rgba(0,0,0,0.3); padding: 4px 8px; border-radius: 4px;">
                        streamlit run dashboard.py
                        </code>
                    </p>
                </div>
            </div>
            """)

        # Event Handlers
        def refresh_status():
            """Refresh all status displays"""
            ds_status, m_status, p_status = control_panel.get_system_status()
            stats = control_panel.get_current_stats()
            return ds_status, m_status, p_status, "Status refreshed", stats

        def start_processing_handler(batch_size_val, max_batches_val, rps_val, mode_val):
            """Handle start processing"""
            return control_panel.start_processing(
                int(batch_size_val), int(max_batches_val), float(rps_val), mode_val
            ) + (control_panel.get_current_stats(),)

        def stop_processing_handler():
            """Handle stop processing"""
            return control_panel.stop_processing() + (control_panel.get_current_stats(),)

        def clear_results_handler():
            """Handle clear results"""
            return control_panel.clear_results() + (control_panel.get_current_stats(),)

        # Wire up event handlers
        start_btn.click(
            fn=start_processing_handler,
            inputs=[batch_size, max_batches, requests_per_second, processing_mode],
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        stop_btn.click(
            fn=stop_processing_handler,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        clear_btn.click(
            fn=clear_results_handler,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        refresh_btn.click(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        # Auto-refresh on load
        demo.load(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        # Auto-refresh every 3 seconds using demo.load with timer
        def auto_refresh():
            """Auto refresh for real-time updates"""
            return refresh_status()

        # Note: Gradio auto-refresh will be handled by manual refresh button
        # Users can click refresh to see updates

    return demo


if __name__ == "__main__":
    demo = create_control_panel()
    demo.launch(server_name="127.0.0.1", server_port=7860, share=False)
