"""
Gradio Control Panel for 5G Security Analytics
"""

import gradio as gr
import json
import os
import sys
import time
import threading
import logging
from pathlib import Path
from typing import Dict, Any, Tuple

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.data_loader import DataLoader
from src.core.batch_processor import BatchProcessor
from src.models.anomaly_detector import AnomalyDetector

logger = logging.getLogger(__name__)

# Shared state file
STATE_FILE = "processing_state.json"

def load_state():
    """Load processing state from file"""
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r') as f:
                return json.load(f)
        except:
            pass
    return {
        "is_processing": False,
        "batch_size": 100,
        "max_batches": 10,
        "requests_per_second": 10.0,
        "processing_mode": "Manual Batch",
        "current_results": [],
        "processing_stats": {
            "total_processed": 0,
            "total_anomalies": 0,
            "current_batch": 0,
            "processing_rate": 0.0
        },
        "log_messages": []
    }

def save_state(state):
    """Save processing state to file"""
    try:
        with open(STATE_FILE, 'w') as f:
            json.dump(state, f)
    except Exception as e:
        logger.error(f"Error saving state: {e}")

def add_log(message: str, level: str = "INFO"):
    """Add log message to state"""
    state = load_state()
    timestamp = time.strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {level}: {message}"
    state["log_messages"].append(log_entry)
    
    # Keep only last 100 messages
    if len(state["log_messages"]) > 100:
        state["log_messages"] = state["log_messages"][-100:]
    
    save_state(state)
    logger.info(message)

class ControlPanel:
    """
    Gradio control panel for managing processing
    """
    
    def __init__(self):
        """Initialize control panel"""
        self.data_loader = None
        self.detector = None
        self.batch_processor = None
        self.processing_thread = None
        
        # Initialize components
        self.initialize_components()
    
    def initialize_components(self):
        """Initialize data loader, detector, and batch processor"""
        try:
            # Initialize data loader
            if os.path.exists("data/sample_inputs/5g_nidd_dataset.csv"):
                self.data_loader = DataLoader("data/sample_inputs/5g_nidd_dataset.csv")
                add_log("Data loader initialized successfully")
            else:
                add_log("Dataset not found", "ERROR")
            
            # Initialize detector
            if os.path.exists("models/rf_models"):
                self.detector = AnomalyDetector("models/rf_models")
                if self.detector.binary_model is not None:
                    self.batch_processor = BatchProcessor(self.detector)
                    add_log("Models loaded successfully")
                else:
                    add_log("Models failed to load", "ERROR")
            else:
                add_log("Models directory not found", "ERROR")
                
        except Exception as e:
            add_log(f"Error initializing components: {e}", "ERROR")
    
    def get_system_status(self) -> Tuple[str, str, str]:
        """Get current system status"""
        # Dataset status
        if self.data_loader and self.data_loader.df is not None:
            dataset_info = self.data_loader.get_dataset_info()
            dataset_status = f"✅ Dataset loaded: {dataset_info['total_records']:,} records"
        else:
            dataset_status = "❌ Dataset not loaded"
        
        # Model status
        if self.detector and self.detector.binary_model is not None:
            model_status = "✅ Models loaded successfully"
        else:
            model_status = "❌ Models not loaded"
        
        # Processing status
        state = load_state()
        if state["is_processing"]:
            processing_status = f"🔄 Processing... Batch {state['processing_stats']['current_batch']}"
        else:
            processing_status = "⏸️ Ready to process"
        
        return dataset_status, model_status, processing_status
    
    def start_processing(self, batch_size: int, max_batches: int, 
                        requests_per_second: float, processing_mode: str) -> Tuple[str, str, str, str]:
        """Start batch processing"""
        if not self.data_loader or not self.batch_processor:
            add_log("Cannot start processing: components not initialized", "ERROR")
            return self.get_system_status() + ("❌ Cannot start: components not ready",)
        
        state = load_state()
        if state["is_processing"]:
            add_log("Processing already running", "WARNING")
            return self.get_system_status() + ("⚠️ Already processing",)
        
        # Update state
        state.update({
            "is_processing": True,
            "batch_size": batch_size,
            "max_batches": max_batches,
            "requests_per_second": requests_per_second,
            "processing_mode": processing_mode,
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            }
        })
        save_state(state)
        
        add_log(f"Starting processing: {batch_size} records/batch, {max_batches} batches max")
        
        # Start processing in background thread
        self.processing_thread = threading.Thread(
            target=self._process_batches,
            args=(batch_size, max_batches, requests_per_second),
            daemon=True
        )
        self.processing_thread.start()
        
        return self.get_system_status() + ("🚀 Processing started",)
    
    def stop_processing(self) -> Tuple[str, str, str, str]:
        """Stop processing"""
        state = load_state()
        state["is_processing"] = False
        save_state(state)
        
        add_log("Processing stopped by user")
        return self.get_system_status() + ("⏹️ Processing stopped",)
    
    def _process_batches(self, batch_size: int, max_batches: int, requests_per_second: float):
        """Process batches in background thread"""
        try:
            inter_record_delay = 1.0 / requests_per_second if requests_per_second > 0 else 0.01
            
            for batch_num in range(max_batches):
                state = load_state()
                if not state["is_processing"]:
                    break
                
                # Get batch data
                batch_df = self.data_loader.get_batch(batch_size)
                if batch_df is None or batch_df.empty:
                    add_log("No more data to process")
                    break
                
                # Update current batch
                state["processing_stats"]["current_batch"] = batch_num + 1
                save_state(state)
                
                # Process batch
                batch_data = batch_df.to_dict('records')
                start_time = time.time()
                
                batch_results = self.batch_processor.process_batch_sequential(batch_data)
                
                processing_time = time.time() - start_time
                
                # Update results and stats
                state["current_results"].extend(batch_results)
                
                # Keep only last 1000 results for memory efficiency
                if len(state["current_results"]) > 1000:
                    state["current_results"] = state["current_results"][-1000:]
                
                anomaly_count = sum(1 for r in batch_results if r.get("is_anomaly", False))
                
                state["processing_stats"]["total_processed"] += len(batch_results)
                state["processing_stats"]["total_anomalies"] += anomaly_count
                state["processing_stats"]["processing_rate"] = len(batch_results) / processing_time if processing_time > 0 else 0
                
                save_state(state)
                
                add_log(f"Batch {batch_num + 1} completed: {len(batch_results)} records, {anomaly_count} anomalies")
                
                # Simulate delay between batches
                time.sleep(inter_record_delay * len(batch_results))
            
            # Mark processing as complete
            state = load_state()
            state["is_processing"] = False
            save_state(state)
            
            add_log(f"Processing completed: {state['processing_stats']['total_processed']} total records")
            
        except Exception as e:
            add_log(f"Error during processing: {e}", "ERROR")
            state = load_state()
            state["is_processing"] = False
            save_state(state)
    
    def clear_results(self) -> Tuple[str, str, str, str]:
        """Clear all results and reset stats"""
        state = load_state()
        state.update({
            "current_results": [],
            "processing_stats": {
                "total_processed": 0,
                "total_anomalies": 0,
                "current_batch": 0,
                "processing_rate": 0.0
            },
            "log_messages": []
        })
        save_state(state)
        
        add_log("Results and statistics cleared")
        return self.get_system_status() + ("🧹 Results cleared",)
    
    def get_current_stats(self) -> str:
        """Get current processing statistics"""
        state = load_state()
        stats = state.get("processing_stats", {})
        
        return f"""Current Statistics:
• Total Processed: {stats.get('total_processed', 0):,} records
• Total Anomalies: {stats.get('total_anomalies', 0):,}
• Current Batch: {stats.get('current_batch', 0)}
• Processing Rate: {stats.get('processing_rate', 0):.1f} records/sec
• Anomaly Rate: {(stats.get('total_anomalies', 0) / max(stats.get('total_processed', 1), 1) * 100):.1f}%"""


def create_control_panel() -> gr.Blocks:
    """Create the Gradio control panel interface"""

    control_panel = ControlPanel()

    with gr.Blocks(title="5G Security Analytics - Control Panel", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🎛️ 5G Security Analytics - Control Panel")
        gr.Markdown("Configure and control the security analytics processing system")

        # System Status Section
        with gr.Row():
            with gr.Column():
                gr.Markdown("### System Status")
                dataset_status = gr.Textbox(label="Dataset Status", interactive=False)
                model_status = gr.Textbox(label="Model Status", interactive=False)
                processing_status = gr.Textbox(label="Processing Status", interactive=False)
                operation_status = gr.Textbox(label="Last Operation", interactive=False)

        # Configuration Section
        gr.Markdown("---")
        gr.Markdown("### Processing Configuration")

        with gr.Row():
            with gr.Column():
                batch_size = gr.Slider(
                    minimum=10, maximum=1000, value=100, step=10,
                    label="Batch Size",
                    info="Number of records to process per batch"
                )
                max_batches = gr.Slider(
                    minimum=1, maximum=100, value=10, step=1,
                    label="Maximum Batches",
                    info="Maximum number of batches to process"
                )

            with gr.Column():
                requests_per_second = gr.Slider(
                    minimum=0.1, maximum=100.0, value=10.0, step=0.1,
                    label="Requests per Second",
                    info="Processing rate (records per second)"
                )
                processing_mode = gr.Dropdown(
                    choices=["Manual Batch", "Continuous Stream"],
                    value="Manual Batch",
                    label="Processing Mode",
                    info="Select processing mode"
                )

        # Control Buttons
        gr.Markdown("---")
        gr.Markdown("### Processing Controls")

        with gr.Row():
            start_btn = gr.Button("🚀 Start Processing", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ Stop Processing", variant="stop", size="lg")
            clear_btn = gr.Button("🧹 Clear Results", variant="secondary", size="lg")
            refresh_btn = gr.Button("🔄 Refresh Status", size="lg")

        # Statistics Display
        gr.Markdown("---")
        gr.Markdown("### Current Statistics")

        stats_display = gr.Textbox(
            label="Processing Statistics",
            lines=6,
            interactive=False
        )

        # Information Section
        gr.Markdown("---")
        with gr.Accordion("ℹ️ Information & Instructions", open=False):
            gr.Markdown("""
            ### How to Use This Control Panel

            1. **Check System Status**: Ensure dataset and models are loaded
            2. **Configure Settings**: Adjust batch size, processing rate, and mode
            3. **Start Processing**: Click "Start Processing" to begin analysis
            4. **Monitor Progress**: Use the Streamlit dashboard for real-time monitoring
            5. **Stop/Clear**: Use controls to stop processing or clear results

            ### Settings Guide

            - **Batch Size**: Larger batches = better throughput, more memory usage
            - **Max Batches**: Limits total processing to prevent overload
            - **Requests/Second**: Controls processing speed (higher = faster)
            - **Processing Mode**: Manual for controlled batches, Continuous for streaming

            ### Dashboard Access

            Open the Streamlit dashboard in another tab for real-time monitoring:
            ```
            streamlit run src/ui/dashboard.py
            ```
            """)

        # Event Handlers
        def refresh_status():
            """Refresh all status displays"""
            ds_status, m_status, p_status = control_panel.get_system_status()
            stats = control_panel.get_current_stats()
            return ds_status, m_status, p_status, "Status refreshed", stats

        def start_processing_handler(batch_size_val, max_batches_val, rps_val, mode_val):
            """Handle start processing"""
            return control_panel.start_processing(
                int(batch_size_val), int(max_batches_val), float(rps_val), mode_val
            ) + (control_panel.get_current_stats(),)

        def stop_processing_handler():
            """Handle stop processing"""
            return control_panel.stop_processing() + (control_panel.get_current_stats(),)

        def clear_results_handler():
            """Handle clear results"""
            return control_panel.clear_results() + (control_panel.get_current_stats(),)

        # Wire up event handlers
        start_btn.click(
            fn=start_processing_handler,
            inputs=[batch_size, max_batches, requests_per_second, processing_mode],
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        stop_btn.click(
            fn=stop_processing_handler,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        clear_btn.click(
            fn=clear_results_handler,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        refresh_btn.click(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

        # Auto-refresh on load
        demo.load(
            fn=refresh_status,
            outputs=[dataset_status, model_status, processing_status, operation_status, stats_display]
        )

    return demo


if __name__ == "__main__":
    demo = create_control_panel()
    demo.launch(server_name="127.0.0.1", server_port=7860, share=False)
